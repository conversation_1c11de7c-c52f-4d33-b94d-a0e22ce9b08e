<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android APP界面设计 - SSID Defake演示</title>
    
    <!-- External CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", <PERSON><PERSON>, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .phone-frame {
            background: linear-gradient(145deg, #2d3748, #4a5568);
            border-radius: 30px;
            padding: 8px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .phone-frame::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 6px;
            background: #1a202c;
            border-radius: 3px;
        }

        .phone-screen {
            background: #ffffff;
            border-radius: 22px;
            overflow: hidden;
            aspect-ratio: 9/19.5;
            position: relative;
        }

        .status-bar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .camera-preview {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.3) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(0,0,0,0.1) 0%, transparent 50%);
            position: relative;
            overflow: hidden;
        }

        .camera-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, rgba(255,255,255,0.3) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.3) 1px, transparent 1px);
            background-size: 33.333% 33.333%;
        }

        .capture-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border: 4px solid #667eea;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .capture-button:active {
            transform: scale(0.95);
            box-shadow: 0 4px 10px rgba(102, 126, 234, 0.5);
        }

        .action-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .action-button.secondary {
            background: linear-gradient(135deg, #718096, #4a5568);
        }

        .progress-ring {
            width: 60px;
            height: 60px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 6px;
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        .notification {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 12px;
            margin: 8px 0;
        }

        .app-state-initial .camera-preview { display: block; }
        .app-state-initial .photo-preview { display: none; }
        .app-state-initial .upload-section { display: none; }

        .app-state-captured .camera-preview { display: none; }
        .app-state-captured .photo-preview { display: block; }
        .app-state-captured .upload-section { display: none; }

        .app-state-uploading .camera-preview { display: none; }
        .app-state-uploading .photo-preview { display: none; }
        .app-state-uploading .upload-section { display: block; }

        .demo-phone {
            width: 300px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="p-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-white mb-4">
            <i class="fas fa-mobile-alt mr-3"></i>
            Android APP界面设计
        </h1>
        <p class="text-xl text-white opacity-90">SSID - Defake深伪检测防御演示</p>
    </div>

    <!-- App States Demo -->
    <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <!-- State 1: Initial Camera View -->
        <div class="text-center">
            <h3 class="text-2xl font-bold text-white mb-4">初始状态</h3>
            <div class="demo-phone phone-frame">
                <div class="phone-screen app-state-initial">
                    <!-- Status Bar -->
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <span class="text-xs">SSID</span>
                        </div>
                        <div class="text-xs">14:32</div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- App Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-shield-alt text-2xl mr-2"></i>
                            <h2 class="text-lg font-bold">SSID Demo</h2>
                        </div>
                        <p class="text-sm opacity-90">Defake深伪检测防御演示</p>
                    </div>

                    <!-- Camera Preview -->
                    <div class="camera-preview flex-1 flex items-center justify-center relative" style="height: 300px;">
                        <div class="camera-grid"></div>
                        <div class="text-center text-gray-600">
                            <i class="fas fa-user text-6xl mb-4 floating-icon"></i>
                            <p class="text-sm">请将人脸置于取景框内</p>
                        </div>
                    </div>

                    <!-- Controls -->
                    <div class="p-6 bg-gray-50">
                        <div class="flex justify-center mb-4">
                            <button class="capture-button flex items-center justify-center">
                                <i class="fas fa-camera text-2xl text-blue-600"></i>
                            </button>
                        </div>
                        <div class="notification">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                <span class="text-sm text-gray-700">点击拍照按钮开始采集人脸</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- State 2: Photo Captured -->
        <div class="text-center">
            <h3 class="text-2xl font-bold text-white mb-4">拍照完成</h3>
            <div class="demo-phone phone-frame">
                <div class="phone-screen app-state-captured">
                    <!-- Status Bar -->
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <span class="text-xs">SSID</span>
                        </div>
                        <div class="text-xs">14:32</div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- App Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-shield-alt text-2xl mr-2"></i>
                            <h2 class="text-lg font-bold">SSID Demo</h2>
                        </div>
                        <p class="text-sm opacity-90">照片预览</p>
                    </div>

                    <!-- Photo Preview -->
                    <div class="photo-preview flex-1 flex items-center justify-center bg-gray-100" style="height: 300px;">
                        <div class="w-48 h-48 bg-gradient-to-br from-gray-300 to-gray-400 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user text-6xl text-gray-600"></i>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="p-6 bg-gray-50">
                        <div class="flex space-x-3 mb-4">
                            <button class="action-button secondary flex-1">
                                <i class="fas fa-redo mr-2"></i>重新拍照
                            </button>
                            <button class="action-button flex-1">
                                <i class="fas fa-check mr-2"></i>确认上传
                            </button>
                        </div>
                        <div class="notification">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                <span class="text-sm text-gray-700">人脸检测成功，可以上传</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- State 3: Uploading -->
        <div class="text-center">
            <h3 class="text-2xl font-bold text-white mb-4">上传处理</h3>
            <div class="demo-phone phone-frame">
                <div class="phone-screen app-state-uploading">
                    <!-- Status Bar -->
                    <div class="status-bar">
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-xs"></i>
                            <span class="text-xs">SSID</span>
                        </div>
                        <div class="text-xs">14:33</div>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-wifi text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-xs"></i>
                        </div>
                    </div>

                    <!-- App Header -->
                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-shield-alt text-2xl mr-2"></i>
                            <h2 class="text-lg font-bold">SSID Demo</h2>
                        </div>
                        <p class="text-sm opacity-90">正在处理中...</p>
                    </div>

                    <!-- Upload Progress -->
                    <div class="upload-section flex-1 flex flex-col items-center justify-center p-8" style="height: 300px;">
                        <div class="progress-ring mb-6"></div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">上传中</h3>
                        <div class="w-full max-w-48 mb-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-2">
                                <span>上传进度</span>
                                <span>75%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="progress-bar rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 text-center">
                            正在上传到服务器进行AI处理<br>
                            请保持网络连接
                        </p>
                    </div>

                    <!-- Status Info -->
                    <div class="p-6 bg-gray-50">
                        <div class="notification">
                            <div class="flex items-center">
                                <i class="fas fa-tv text-blue-600 mr-2"></i>
                                <span class="text-sm text-gray-700">请关注大屏幕查看处理结果</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Design Specifications -->
    <div class="max-w-4xl mx-auto mt-16 bg-white bg-opacity-10 backdrop-blur-lg rounded-3xl p-8">
        <h2 class="text-3xl font-bold text-white mb-8 text-center">
            <i class="fas fa-palette mr-3"></i>
            设计规范
        </h2>
        
        <div class="grid md:grid-cols-2 gap-8">
            <div>
                <h3 class="text-xl font-bold text-white mb-4">视觉设计</h3>
                <ul class="space-y-2 text-white opacity-90">
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        Material Design设计语言
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        渐变色彩主题 (蓝紫色系)
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        大按钮设计，适合展会操作
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        清晰的状态反馈系统
                    </li>
                </ul>
            </div>
            
            <div>
                <h3 class="text-xl font-bold text-white mb-4">交互特性</h3>
                <ul class="space-y-2 text-white opacity-90">
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        直观的拍照和预览流程
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        实时进度显示和状态更新
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        友好的错误提示和引导
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-400"></i>
                        与大屏幕展示的联动提示
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- JavaScript for Demo -->
    <script>
        // 简单的演示交互
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些简单的交互效果
            const captureButtons = document.querySelectorAll('.capture-button');
            const actionButtons = document.querySelectorAll('.action-button');
            
            captureButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
            
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'translateY(-2px)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(0)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
