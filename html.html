<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocket STOMP Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
	<style>
        #imagePreview, #videoPreview {
            margin-top: 20px;
            border: 1px solid #ccc;
            padding: 10px;
            max-width: 600px;
        }
    </style>
</head>
<body>
<h2>WebSocket Demo</h2>


<!--
<p>

    <button onclick="sendMessage()">发送消息</button>
</p>
-->

<!-- 👇 新增：图片显示区域 -->
<div id="imagePreview">
    <h3>图片预览</h3>
    <img id="imageElement" src="" alt="图片" style="max-width: 100%; display: none;">
</div>
<!-- 👇 新增：视频播放区域 -->
<div id="videoPreview">
    <h3>视频预览</h3>
    <video id="videoElement" controls style="max-width: 100%; display: none;"></video>
</div>
<pre id="log"></pre>

<script>
    let stompClient = null;

    function log(msg) {
        document.getElementById('log').textContent += msg + '\n';
    }

    function connect() {
        const socket = new SockJS('http://************:8701/ws');  // SpringBoot配置的端点
        stompClient = Stomp.over(socket);


        stompClient.connect({ 'device-type': 'HTML' }, frame => {
            log("✅ 连接成功: " + frame);

            // 订阅私有消息
            stompClient.subscribe('/user/html/topic/msg', message => {
                log("📩 收到消息: " + message.body);
				try {
                    const data = JSON.parse(message.body);

                    // 显示图片
                    if (data.imageBase64 && data.imageBase64.trim() !== "") {
                        const img = document.getElementById("imageElement");
                        img.src = "data:image/png;base64," + data.imageBase64;
                        img.style.display = "block";
                    }

                    // 显示视频
                    if (data.videoBase64 && data.videoBase64.trim() !== "") {
                        const video = document.getElementById("videoElement");
                        video.src = "data:video/mp4;base64," + data.videoBase64;
                        video.style.display = "block";
                        video.load(); // 重新加载新视频
                    }

                } catch (e) {
                    log("⚠️ 解析消息失败: " + e);
                }
            });

        }, error => {
            log("❌ 连接失败: " + error);
        });
    }

    function sendMessage() {
        const content = document.getElementById('message').value;
        const deviceType = document.getElementById('deviceType').value;

        const msg = {
            type: "this is type",
            imageBase64: "this is base64"
        };

        // 向服务端发送控制消息，路径去掉 /url/a 前缀部分，由 Spring 处理
        stompClient.send('/web/topic/html', {}, JSON.stringify(msg));
        log("📤 发送消息: " + JSON.stringify(msg));
    }

    connect();
</script>
</body>
</html>

