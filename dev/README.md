# WAIC Demo 后端服务（dev）

本目录为后续开发的人脸驱动视频合成后端服务。

## 主要功能
- 提供API接口，接收人脸图片和动作列表，生成包含指定动作（点头、摇头、张嘴、眨眼）的视频。
- 依赖LivePortrait项目核心代码进行视频合成。

## 快速启动
```bash
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

## API说明
### POST /generate_video/
- 参数：
  - image: 人脸图片（multipart/form-data）
  - actions: 动作列表（如["nod", "shake", "mouth", "blink"]，可选）
- 返回：
  - 生成的视频文件（video/mp4），或处理中的JSON状态

## 后续开发建议
- 集成LivePortrait调用，自动生成动作驱动序列
- 支持异步处理与进度查询
- 增加异常处理和日志记录