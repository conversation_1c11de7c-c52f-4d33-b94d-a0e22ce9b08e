<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .controls {
            margin: 20px 0;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WAIC Demo WebSocket连接测试</h1>
        
        <div id="connectionStatus" class="status warning">
            状态：未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">连接WebSocket</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button id="clearLogBtn" onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="controls">
            <h3>测试功能</h3>
            <button id="startDemoBtn" onclick="sendStartDemo()" disabled>发送开始演示</button>
            <button id="enableUploadBtn" onclick="sendEnableUpload()" disabled>发送启用拍照</button>
            <br>
            <input type="file" id="fileInput" accept="image/*" disabled>
            <button id="uploadBtn" onclick="uploadImage()" disabled>上传图片</button>
        </div>
        
        <h3>连接日志</h3>
        <div id="log"></div>
    </div>

    <script>
        let stompClient = null;
        let selectedFileBase64 = null;
        
        const serverUrl = 'http://************:8701/ws';
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = `状态：${message}`;
            statusElement.className = `status ${type}`;
        }
        
        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('startDemoBtn').disabled = !connected;
            document.getElementById('enableUploadBtn').disabled = !connected;
            document.getElementById('fileInput').disabled = !connected;
            document.getElementById('uploadBtn').disabled = !connected || !selectedFileBase64;
        }
        
        function connect() {
            try {
                log(`尝试连接到: ${serverUrl}`);
                updateStatus('连接中...', 'warning');
                
                const socket = new SockJS(serverUrl);
                stompClient = Stomp.over(socket);
                
                // 禁用调试日志
                stompClient.debug = null;
                
                stompClient.connect(
                    { 'device-type': 'HTML' },
                    (frame) => {
                        log('✅ WebSocket连接成功');
                        log(`连接帧信息: ${frame}`);
                        updateStatus('已连接', 'success');
                        updateButtons(true);
                        
                        // 订阅消息
                        stompClient.subscribe('/user/html/topic/msg', (message) => {
                            log(`📩 收到消息: ${message.body}`);
                            try {
                                const data = JSON.parse(message.body);
                                handleMessage(data);
                            } catch (e) {
                                log(`⚠️ 解析消息失败: ${e.message}`);
                            }
                        });
                        
                        log('📡 已订阅消息频道: /user/html/topic/msg');
                    },
                    (error) => {
                        log(`❌ 连接失败: ${error}`);
                        updateStatus('连接失败', 'error');
                        updateButtons(false);
                    }
                );
            } catch (error) {
                log(`❌ 连接异常: ${error.message}`);
                updateStatus('连接异常', 'error');
                updateButtons(false);
            }
        }
        
        function disconnect() {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect(() => {
                    log('🔌 已断开WebSocket连接');
                    updateStatus('已断开', 'warning');
                    updateButtons(false);
                });
            }
        }
        
        function sendStartDemo() {
            if (!stompClient || !stompClient.connected) {
                log('⚠️ WebSocket未连接');
                return;
            }
            
            const message = { type: 'startDemo' };
            try {
                stompClient.send('/web/topic/html', {}, JSON.stringify(message));
                log(`📤 发送开始演示消息: ${JSON.stringify(message)}`);
            } catch (error) {
                log(`❌ 发送消息失败: ${error.message}`);
            }
        }
        
        function sendEnableUpload() {
            if (!stompClient || !stompClient.connected) {
                log('⚠️ WebSocket未连接');
                return;
            }
            
            const message = { type: 'enableUpload' };
            try {
                stompClient.send('/web/topic/html', {}, JSON.stringify(message));
                log(`📤 发送启用拍照消息: ${JSON.stringify(message)}`);
            } catch (error) {
                log(`❌ 发送消息失败: ${error.message}`);
            }
        }
        
        function uploadImage() {
            if (!stompClient || !stompClient.connected) {
                log('⚠️ WebSocket未连接');
                return;
            }
            
            if (!selectedFileBase64) {
                log('⚠️ 请先选择图片文件');
                return;
            }
            
            const message = {
                type: 'upload',
                imageBase64: selectedFileBase64.split(',')[1] // 移除data:image/...;base64,前缀
            };
            
            try {
                stompClient.send('/web/topic/html', {}, JSON.stringify(message));
                log(`📤 发送图片上传消息 (${Math.round(selectedFileBase64.length/1024)}KB)`);
            } catch (error) {
                log(`❌ 发送图片失败: ${error.message}`);
            }
        }
        
        function handleMessage(data) {
            log(`📋 处理消息类型: ${data.type || '未知'}`);
            
            if (data.imageBase64) {
                log(`🖼️ 收到图片数据 (${Math.round(data.imageBase64.length/1024)}KB)`);
            }
            
            if (data.videoBase64) {
                log(`🎥 收到视频数据 (${Math.round(data.videoBase64.length/1024)}KB)`);
            }
            
            if (data.progress) {
                log(`📊 处理进度: ${data.progress}%`);
            }
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function() {
            const file = this.files[0];
            if (!file) {
                selectedFileBase64 = null;
                document.getElementById('uploadBtn').disabled = true;
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                log('⚠️ 请选择图片文件');
                selectedFileBase64 = null;
                document.getElementById('uploadBtn').disabled = true;
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                selectedFileBase64 = e.target.result;
                document.getElementById('uploadBtn').disabled = false;
                log(`📸 已选择图片文件: ${file.name} (${Math.round(file.size/1024)}KB)`);
            };
            reader.readAsDataURL(file);
        });
        
        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('🚀 WebSocket测试页面已加载');
            log(`📡 目标服务器: ${serverUrl}`);
            updateStatus('未连接', 'warning');
            updateButtons(false);
        });
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect();
            }
        });
    </script>
</body>
</html>
