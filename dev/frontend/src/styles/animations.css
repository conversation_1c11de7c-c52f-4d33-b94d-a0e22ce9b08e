/* 科技感动画效果 */

/* 脉冲发光效果 */
@keyframes pulseGlow {
  from { 
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
    transform: scale(1);
  }
  to { 
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.8);
    transform: scale(1.02);
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
}

/* 扫描线动画 */
@keyframes scan {
  0% { transform: translateY(0); opacity: 1; }
  50% { opacity: 0.8; }
  100% { transform: translateY(426px); opacity: 1; }
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: scan 4s linear infinite;
  z-index: 10;
}

/* 加载点动画 */
@keyframes loadingDots {
  0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots div {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #00d4ff;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots div:nth-child(2) { animation-delay: -0.16s; }
.loading-dots div:nth-child(3) { animation-delay: 0s; }

/* 状态指示器脉冲 */
@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

.status-pulse {
  animation: pulse 1.5s infinite;
}

/* 打字机效果 */
@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #00d4ff; }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid #00d4ff;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

/* 进度条动画 */
.progress-bar {
  background: linear-gradient(90deg, #0066ff, #00d4ff);
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

/* 按钮点击效果 */
.btn-click {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.btn-click:active {
  transform: scale(0.98);
}

.btn-click::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
}

.btn-click:active::before {
  width: 300px;
  height: 300px;
}

/* 数据流动效果 */
@keyframes dataFlow {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

.data-flow {
  position: relative;
  overflow: hidden;
}

.data-flow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: dataFlow 3s infinite;
}

/* 全息效果 */
@keyframes hologram {
  0%, 100% { 
    opacity: 1;
    filter: hue-rotate(0deg);
  }
  25% { 
    opacity: 0.8;
    filter: hue-rotate(90deg);
  }
  50% { 
    opacity: 0.9;
    filter: hue-rotate(180deg);
  }
  75% { 
    opacity: 0.8;
    filter: hue-rotate(270deg);
  }
}

.hologram-effect {
  animation: hologram 4s infinite;
}

/* 电路板效果 */
.circuit-bg {
  background-image: 
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: circuitMove 20s linear infinite;
}

@keyframes circuitMove {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

/* 能量波动效果 */
@keyframes energyWave {
  0% { 
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
    transform: scale(1);
  }
}

.energy-wave {
  animation: energyWave 2s infinite;
}

/* 矩阵雨效果（简化版） */
@keyframes matrixRain {
  0% { transform: translateY(-100%); opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

.matrix-rain {
  position: fixed;
  top: 0;
  color: #00d4ff;
  font-family: monospace;
  font-size: 12px;
  animation: matrixRain 3s linear infinite;
  pointer-events: none;
  z-index: 1;
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
