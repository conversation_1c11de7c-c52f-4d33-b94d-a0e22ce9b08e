import { ref, reactive } from 'vue'
import websocketService from './websocket.js'

class DemoController {
  constructor() {
    // 演示状态
    this.isRunning = ref(false)
    this.isAppConnected = ref(false)
    this.isUploadEnabled = ref(false)
    this.currentStep = ref(0)
    
    // 自动重启相关
    this.autoRestartTimer = null
    this.countdownTimer = null
    this.countdownSeconds = ref(0)
    
    // 演示数据
    this.demoData = reactive({
      originalPhoto: null,
      generatedVideo: null,
      videoProgress: 0,
      detectionResult: null,
      aiAnalysis: {
        status: 'waiting',
        text: '等待分析',
        content: ''
      }
    })
    
    // 演示状态
    this.demoStatus = reactive({
      type: 'waiting',
      text: '系统就绪'
    })
    
    // APP连接状态
    this.appStatus = reactive({
      connected: false,
      text: 'APP未连接'
    })
    
    // 进度步骤
    this.progressSteps = reactive([
      { id: 0, name: 'APP连接', status: 'waiting', time: '未连接', icon: 'mobile-alt' },
      { id: 1, name: '用户拍照', status: 'waiting', time: '等待中', icon: 'camera' },
      { id: 2, name: '人脸检测', status: 'waiting', time: '等待中', icon: 'search' },
      { id: 3, name: '视频生成', status: 'waiting', time: '等待中', icon: 'cog' },
      { id: 4, name: '深伪检测', status: 'waiting', time: '等待中', icon: 'shield-alt' },
      { id: 5, name: 'AI分析', status: 'waiting', time: '等待中', icon: 'brain' }
    ])
    
    // 演示步骤配置
    this.steps = [
      { name: 'appConnect', duration: 0 },
      { name: 'photoUpload', duration: 0 },
      { name: 'faceDetection', duration: 2000 },
      { name: 'videoGeneration', duration: 15000 },
      { name: 'deepfakeDetection', duration: 8000 },
      { name: 'aiExplanation', duration: 5000 }
    ]
    
    this.initWebSocketHandlers()
  }
  
  // 初始化WebSocket事件处理
  initWebSocketHandlers() {
    websocketService.on('onConnect', () => {
      console.log('WebSocket连接成功，模拟APP连接')
      this.simulateAppConnection()
    })
    
    websocketService.on('onMessage', (data) => {
      this.handleWebSocketMessage(data)
    })
    
    websocketService.on('onError', (error) => {
      console.error('WebSocket错误:', error)
      this.appStatus.connected = false
      this.appStatus.text = 'APP连接失败'
    })
  }
  
  // 模拟APP连接
  simulateAppConnection() {
    setTimeout(() => {
      this.isAppConnected.value = true
      this.appStatus.connected = true
      this.appStatus.text = 'APP已连接'
      this.updateProgressStep(0, 'complete', 'APP连接成功')
      this.demoStatus.type = 'waiting'
      this.demoStatus.text = '系统就绪，可开始演示'
    }, 2000)
  }
  
  // 处理WebSocket消息
  handleWebSocketMessage(data) {
    console.log('处理WebSocket消息:', data)
    
    // 处理图片数据
    if (data.imageBase64 && data.imageBase64.trim() !== "") {
      this.demoData.originalPhoto = "data:image/png;base64," + data.imageBase64
      this.updateProgressStep(1, 'complete', '照片上传成功')
      this.onPhotoUploaded()
    }
    
    // 处理视频数据
    if (data.videoBase64 && data.videoBase64.trim() !== "") {
      this.demoData.generatedVideo = "data:video/mp4;base64," + data.videoBase64
      this.updateProgressStep(3, 'complete', '视频生成完成')
      this.demoData.videoProgress = 100
    }
    
    // 处理其他状态更新
    if (data.type) {
      this.handleStatusUpdate(data)
    }
  }
  
  // 状态更新处理
  handleStatusUpdate(data) {
    switch (data.type) {
      case 'faceDetection':
        this.updateProgressStep(2, 'processing', '人脸检测中')
        break
      case 'videoGeneration':
        this.updateProgressStep(3, 'processing', '视频生成中')
        if (data.progress) {
          this.demoData.videoProgress = data.progress
        }
        break
      case 'deepfakeDetection':
        this.updateProgressStep(4, 'processing', '深伪检测中')
        break
      case 'aiAnalysis':
        this.updateProgressStep(5, 'processing', 'AI分析中')
        this.demoData.aiAnalysis.status = 'processing'
        this.demoData.aiAnalysis.text = '分析中'
        break
      case 'detectionResult':
        this.demoData.detectionResult = data.result
        this.updateProgressStep(4, 'complete', '检测完成')
        break
      case 'analysisResult':
        this.demoData.aiAnalysis = {
          status: 'complete',
          text: '分析完成',
          content: data.content
        }
        this.updateProgressStep(5, 'complete', 'AI分析完成')
        this.onDemoComplete()
        break
    }
  }
  
  // 更新进度步骤
  updateProgressStep(stepIndex, status, customText = null) {
    const step = this.progressSteps[stepIndex]
    if (!step) return
    
    step.status = status
    if (customText) {
      step.name = customText
    }
    
    if (status === 'complete') {
      const now = new Date()
      step.time = now.toLocaleTimeString('zh-CN', { hour12: false })
    } else if (status === 'processing') {
      step.time = '进行中...'
    }
  }
  
  // 开始新演示
  startNewDemo() {
    console.log('开始新演示')
    this.clearCountdown()
    this.resetDemo()
    
    if (this.isAppConnected.value) {
      this.demoStatus.type = 'waiting'
      this.demoStatus.text = '重置完成，准备新演示'
      
      // 1.5秒后自动开启拍照功能
      setTimeout(() => {
        this.enablePhotoUpload()
      }, 1500)
    }
    
    // 发送开始演示消息到后端
    websocketService.sendMessage('/web/topic/html', {
      type: 'startDemo'
    })
  }
  
  // 启用拍照上传
  enablePhotoUpload() {
    if (!this.isAppConnected.value) {
      console.warn('APP未连接，无法开启拍照功能')
      return
    }
    
    this.isUploadEnabled.value = true
    this.demoStatus.type = 'waiting'
    this.demoStatus.text = '等待用户拍照上传'
    this.updateProgressStep(1, 'processing', '等待用户拍照')
    
    // 发送启用拍照消息到后端
    websocketService.sendMessage('/web/topic/html', {
      type: 'enableUpload'
    })
  }
  
  // 照片上传完成回调
  onPhotoUploaded() {
    if (!this.isUploadEnabled.value) return
    
    this.isRunning.value = true
    this.demoStatus.type = 'processing'
    this.demoStatus.text = '演示进行中'
    
    // 开始后续处理流程（模拟）
    this.executeStep(2) // 从人脸检测开始
  }
  
  // 执行演示步骤（模拟）
  executeStep(stepIndex) {
    if (stepIndex >= this.steps.length) {
      return
    }
    
    const step = this.steps[stepIndex]
    
    switch(step.name) {
      case 'faceDetection':
        this.simulateFaceDetection()
        break
      case 'videoGeneration':
        this.simulateVideoGeneration()
        break
      case 'deepfakeDetection':
        this.simulateDeepfakeDetection()
        break
      case 'aiExplanation':
        this.simulateAIExplanation()
        break
    }
    
    if (step.duration > 0) {
      setTimeout(() => {
        this.executeStep(stepIndex + 1)
      }, step.duration)
    }
  }
  
  // 模拟人脸检测
  simulateFaceDetection() {
    this.updateProgressStep(2, 'processing', '人脸检测中')
    setTimeout(() => {
      this.updateProgressStep(2, 'complete', '人脸检测完成')
    }, 2000)
  }
  
  // 模拟视频生成
  simulateVideoGeneration() {
    this.updateProgressStep(3, 'processing', '视频生成中')
    
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 8
      if (progress > 100) progress = 100
      
      this.demoData.videoProgress = Math.round(progress)
      
      if (progress >= 100) {
        clearInterval(interval)
        this.updateProgressStep(3, 'complete', '视频生成完成')
        // 这里应该有实际的视频数据，暂时模拟
      }
    }, 500)
  }
  
  // 模拟深伪检测
  simulateDeepfakeDetection() {
    this.updateProgressStep(4, 'processing', '深伪检测中')
    setTimeout(() => {
      this.demoData.detectionResult = {
        score: '95.7',
        label: '检测为合成内容',
        riskLevel: '高风险'
      }
      this.updateProgressStep(4, 'complete', '检测完成')
    }, 8000)
  }
  
  // 模拟AI分析
  simulateAIExplanation() {
    this.updateProgressStep(5, 'processing', 'AI分析中')
    this.demoData.aiAnalysis.status = 'processing'
    this.demoData.aiAnalysis.text = '分析中'
    
    setTimeout(() => {
      const analysisContent = `基于深度学习模型的综合分析结果：

经过多维度技术检测，该视频存在明显的人工合成特征。检测算法通过分析视频帧间的连续性、面部特征的自然度、以及整体视觉一致性等关键指标，确定该内容为AI生成的深伪视频。

【详细分析维度】

1. 面部特征一致性检测
   - 检测到面部轮廓与原始照片存在细微差异
   - 眼部区域的纹理细节不够自然
   - 鼻梁阴影与光照方向不完全匹配

2. 光照与阴影合理性分析
   - 面部光照分布存在不自然的渐变
   - 左侧脸颊阴影过于锐利，缺乏自然过渡
   - 眼窝阴影深度与环境光照不符

3. 边缘模糊度与自然度评估
   - 发际线边缘存在明显的人工处理痕迹
   - 面部与背景交界处模糊度异常
   - 耳朵轮廓细节缺失

【最终判定】
置信度：95.7%
风险等级：高风险
建议处理：标记为合成媒体内容`
      
      this.demoData.aiAnalysis = {
        status: 'complete',
        text: '分析完成',
        content: analysisContent
      }
      this.updateProgressStep(5, 'complete', 'AI分析完成')
      this.onDemoComplete()
    }, 5000)
  }
  
  // 演示完成
  onDemoComplete() {
    this.isRunning.value = false
    this.demoStatus.type = 'complete'
    this.demoStatus.text = '演示完成'
    
    // 开始2分钟倒计时，自动开始新演示
    this.startAutoRestartCountdown()
  }
  
  // 开始自动重启倒计时
  startAutoRestartCountdown() {
    this.countdownSeconds.value = 120 // 2分钟
    
    this.clearCountdown()
    
    // 更新倒计时显示
    this.countdownTimer = setInterval(() => {
      if (this.countdownSeconds.value > 0) {
        this.countdownSeconds.value--
      } else {
        this.clearCountdown()
        if (!this.isRunning.value) {
          this.startNewDemo()
        }
      }
    }, 1000)
    
    // 2分钟后自动开始新演示
    this.autoRestartTimer = setTimeout(() => {
      if (!this.isRunning.value) {
        this.clearCountdown()
        this.startNewDemo()
      }
    }, 120000)
  }
  
  // 清除倒计时
  clearCountdown() {
    if (this.autoRestartTimer) {
      clearTimeout(this.autoRestartTimer)
      this.autoRestartTimer = null
    }
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
    this.countdownSeconds.value = 0
  }
  
  // 重置演示
  resetDemo() {
    this.isRunning.value = false
    this.isUploadEnabled.value = false
    this.currentStep.value = 0
    
    // 重置数据
    this.demoData.originalPhoto = null
    this.demoData.generatedVideo = null
    this.demoData.videoProgress = 0
    this.demoData.detectionResult = null
    this.demoData.aiAnalysis = {
      status: 'waiting',
      text: '等待分析',
      content: ''
    }
    
    // 重置进度步骤
    this.progressSteps.forEach((step, index) => {
      if (index === 0 && this.isAppConnected.value) {
        step.status = 'complete'
        step.name = 'APP连接成功'
        step.time = new Date().toLocaleTimeString('zh-CN', { hour12: false })
      } else {
        step.status = 'waiting'
        step.time = '等待中'
        // 恢复原始名称
        const originalNames = ['APP连接', '用户拍照', '人脸检测', '视频生成', '深伪检测', 'AI分析']
        step.name = originalNames[index]
      }
    })
  }
  
  // 获取重置按钮文本
  getResetButtonText() {
    if (this.countdownSeconds.value > 0) {
      const minutes = Math.floor(this.countdownSeconds.value / 60)
      const seconds = this.countdownSeconds.value % 60
      const timeStr = `${minutes}:${seconds.toString().padStart(2, '0')}`
      return `开始新演示 (${timeStr}后自动开始)`
    }
    return '开始新演示'
  }
}

// 创建单例实例
const demoController = new DemoController()

export default demoController
