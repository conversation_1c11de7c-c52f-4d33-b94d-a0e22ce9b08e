import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

class WebSocketService {
  constructor() {
    this.stompClient = null
    this.isConnected = false
    this.callbacks = {
      onConnect: [],
      onDisconnect: [],
      onMessage: [],
      onError: []
    }
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
  }

  // 添加事件监听器
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback)
    }
  }

  // 移除事件监听器
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback)
      if (index > -1) {
        this.callbacks[event].splice(index, 1)
      }
    }
  }

  // 触发事件
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data))
    }
  }

  // 连接WebSocket
  connect(serverUrl = 'http://10.151.69.46:8701/ws') {
    try {
      const socket = new SockJS(serverUrl)
      this.stompClient = Stomp.over(socket)
      
      // 禁用调试日志
      this.stompClient.debug = null

      this.stompClient.connect(
        { 'device-type': 'HTML' },
        (frame) => {
          console.log('✅ WebSocket连接成功:', frame)
          this.isConnected = true
          this.reconnectAttempts = 0
          this.emit('onConnect', frame)
          this.subscribeToMessages()
        },
        (error) => {
          console.error('❌ WebSocket连接失败:', error)
          this.isConnected = false
          this.emit('onError', error)
          this.handleReconnect()
        }
      )
    } catch (error) {
      console.error('❌ WebSocket初始化失败:', error)
      this.emit('onError', error)
    }
  }

  // 订阅消息
  subscribeToMessages() {
    if (!this.stompClient || !this.isConnected) return

    // 订阅私有消息频道
    this.stompClient.subscribe('/user/html/topic/msg', (message) => {
      try {
        const data = JSON.parse(message.body)
        console.log('📩 收到WebSocket消息:', data)
        this.emit('onMessage', data)
      } catch (error) {
        console.error('⚠️ 解析WebSocket消息失败:', error)
        this.emit('onError', error)
      }
    })
  }

  // 发送消息
  sendMessage(destination, message) {
    if (!this.stompClient || !this.isConnected) {
      console.warn('⚠️ WebSocket未连接，无法发送消息')
      return false
    }

    try {
      this.stompClient.send(destination, {}, JSON.stringify(message))
      console.log('📤 发送WebSocket消息:', message)
      return true
    } catch (error) {
      console.error('❌ 发送WebSocket消息失败:', error)
      this.emit('onError', error)
      return false
    }
  }

  // 断开连接
  disconnect() {
    if (this.stompClient && this.isConnected) {
      this.stompClient.disconnect(() => {
        console.log('🔌 WebSocket已断开连接')
        this.isConnected = false
        this.emit('onDisconnect')
      })
    }
  }

  // 处理重连
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`🔄 尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    } else {
      console.error('❌ WebSocket重连失败，已达到最大重试次数')
    }
  }

  // 获取连接状态
  getConnectionStatus() {
    return this.isConnected
  }
}

// 创建单例实例
const websocketService = new WebSocketService()

export default websocketService
