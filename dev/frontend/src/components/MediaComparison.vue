<template>
  <div class="card-glow rounded-2xl p-6 flex-1">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-bold text-white">
        <i class="fas fa-exchange-alt mr-2 text-green-400"></i>
        原图与合成视频对比
      </h3>
      <div class="flex items-center space-x-4">
        <button 
          v-if="showManualUpload"
          @click="$emit('enable-upload')"
          class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-sm font-medium"
        >
          <i class="fas fa-mobile-alt mr-2"></i>手动开启拍照
        </button>
        <button
          @click="$emit('start-demo')"
          class="px-6 py-3 bg-orange-600 hover:bg-orange-700 rounded-lg transition-all duration-300 text-sm font-medium btn-click energy-wave"
        >
          <i class="fas fa-redo mr-2"></i>{{ resetButtonText }}
        </button>
      </div>
    </div>
    
    <!-- 原图与视频对比容器 -->
    <div class="flex justify-center items-center h-full space-x-8">
      <!-- 原始照片 -->
      <div class="flex flex-col items-center">
        <h4 class="text-lg font-medium text-gray-300 mb-3">原始照片</h4>
        <div
          class="bg-gray-800 rounded-xl flex items-center justify-center relative overflow-hidden card-hover"
          style="width: 240px; height: 426px;"
          :class="{ 'pulse-glow': !originalPhoto && appConnected }"
        >
          <div v-if="!originalPhoto" class="text-center text-gray-400">
            <i :class="photoIconClass" class="text-4xl mb-4 floating-icon"></i>
            <p class="text-base mb-2">{{ photoStatusText.title }}</p>
            <p class="text-sm">{{ photoStatusText.subtitle }}</p>
            <div v-if="photoStatusText.title === '拍照功能已开启'" class="loading-dots mt-4">
              <div></div>
              <div></div>
              <div></div>
            </div>
          </div>
          <img
            v-else
            :src="originalPhoto"
            class="w-full h-full object-cover rounded-xl transition-all duration-500"
            alt="原始照片"
          />
          <div v-if="originalPhoto" class="absolute top-2 right-2">
            <div class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
              <i class="fas fa-check mr-1"></i>已上传
            </div>
          </div>
        </div>
      </div>

      <!-- 箭头指示 -->
      <div class="flex flex-col items-center">
        <i class="fas fa-arrow-right text-3xl text-cyan-400 floating-icon"></i>
        <span class="text-sm text-gray-400 mt-3 font-medium">AI生成</span>
      </div>

      <!-- 合成视频 -->
      <div class="flex flex-col items-center">
        <h4 class="text-lg font-medium text-gray-300 mb-3">合成视频</h4>
        <div
          class="video-container rounded-xl flex items-center justify-center relative overflow-hidden card-hover"
          style="width: 240px; height: 426px;"
          :class="{ 'hologram-effect': generatedVideo }"
        >
          <div v-if="!generatedVideo" class="text-center text-gray-400">
            <i :class="videoIconClass" class="text-4xl mb-4 floating-icon"></i>
            <p class="text-base mb-2">{{ videoStatusText.title }}</p>
            <p class="text-sm mb-4">{{ videoStatusText.subtitle }}</p>
            <div v-if="showVideoProgress" class="w-full px-4">
              <div class="w-full bg-gray-600 rounded-full h-2 mb-2 relative overflow-hidden">
                <div
                  class="progress-bar h-2 rounded-full transition-all duration-500"
                  :style="`width: ${videoProgress}%`"
                ></div>
              </div>
              <span class="text-xs text-gray-400">{{ videoProgress }}%</span>
            </div>
          </div>
          <video
            v-else
            :src="generatedVideo"
            class="w-full h-full object-cover rounded-xl transition-all duration-500"
            controls
            autoplay
            loop
          ></video>
          <div v-if="showScanLine" class="scan-line"></div>
          <div v-if="generatedVideo" class="absolute top-2 left-2">
            <div class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium pulse-glow">
              <i class="fas fa-play mr-1"></i>AI生成
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MediaComparison',
  props: {
    originalPhoto: {
      type: String,
      default: null
    },
    generatedVideo: {
      type: String,
      default: null
    },
    videoProgress: {
      type: Number,
      default: 0
    },
    appConnected: {
      type: Boolean,
      default: false
    },
    demoStatus: {
      type: String,
      default: 'waiting'
    }
  },
  emits: ['start-demo', 'enable-upload'],
  computed: {
    showManualUpload() {
      // 通常情况下隐藏手动开启按钮
      return false
    },
    resetButtonText() {
      return '开始新演示'
    },
    photoIconClass() {
      if (this.appConnected) {
        return 'fas fa-mobile-alt text-green-400'
      }
      return 'fas fa-mobile-alt'
    },
    photoStatusText() {
      if (!this.appConnected) {
        return {
          title: '等待APP连接',
          subtitle: '正在连接手机APP...'
        }
      }
      if (this.originalPhoto) {
        return {
          title: '照片上传成功',
          subtitle: '开始处理...'
        }
      }
      if (this.demoStatus === 'waiting') {
        return {
          title: 'APP已连接',
          subtitle: '点击"开始新演示"开始'
        }
      }
      return {
        title: '拍照功能已开启',
        subtitle: '请使用手机APP拍照'
      }
    },
    videoIconClass() {
      if (this.generatedVideo) {
        return 'fas fa-check-circle text-green-400'
      }
      if (this.showVideoProgress) {
        return 'fas fa-cog text-blue-400 fa-spin'
      }
      return 'fas fa-video'
    },
    videoStatusText() {
      if (this.generatedVideo) {
        return {
          title: '视频生成完成',
          subtitle: '点击播放查看'
        }
      }
      if (this.showVideoProgress) {
        return {
          title: '正在生成视频',
          subtitle: '请稍候...'
        }
      }
      return {
        title: '等待视频生成',
        subtitle: '需要先完成拍照上传'
      }
    },
    showVideoProgress() {
      return this.videoProgress > 0 && !this.generatedVideo
    },
    showScanLine() {
      return this.showVideoProgress
    }
  }
}
</script>

<style scoped>
.card-glow {
  background: rgba(26, 31, 46, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-container {
  background: radial-gradient(circle at center, #1e293b 0%, #0f172a 100%);
  border: 2px solid #334155;
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.progress-bar {
  background: linear-gradient(90deg, #0066ff, #00d4ff);
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: scan 4s linear infinite;
}

@keyframes scan {
  0% { transform: translateY(0); }
  100% { transform: translateY(426px); }
}
</style>
