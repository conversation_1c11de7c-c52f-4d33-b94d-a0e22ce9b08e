<template>
  <div class="card-glow rounded-2xl p-6 card-hover">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-bold text-white data-flow">
        <i class="fas fa-timeline mr-2 text-orange-400"></i>
        处理进度
      </h3>
      <i class="fas fa-cogs text-2xl text-purple-400 floating-icon energy-wave"></i>
    </div>
    <div class="space-y-4">
      <div 
        v-for="step in steps" 
        :key="step.id"
        class="flex items-center space-x-3"
      >
        <div
          :class="[
            'w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500',
            getStepClass(step.status)
          ]"
        >
          <i
            v-if="step.status === 'complete'"
            class="fas fa-check text-xs"
          ></i>
          <i
            v-else
            :class="`fas fa-${step.icon} text-xs`"
            :style="step.status === 'processing' ? 'animation: float 2s infinite' : ''"
          ></i>
        </div>
        <div class="flex-1">
          <div 
            :class="[
              'font-medium text-sm',
              getTextClass(step.status)
            ]"
          >
            {{ step.name }}
          </div>
          <div 
            :class="[
              'text-xs',
              getTimeClass(step.status)
            ]"
          >
            {{ step.time }}
          </div>
          <!-- 视频生成进度条 -->
          <div v-if="step.id === 3 && step.status === 'processing'" class="flex items-center mt-1">
            <div class="w-16 bg-gray-600 rounded-full h-2 mr-2">
              <div 
                class="progress-bar h-2 rounded-full" 
                :style="`width: ${videoProgress}%`"
              ></div>
            </div>
            <span class="text-gray-500 text-xs">{{ videoProgress }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProgressTimeline',
  props: {
    steps: {
      type: Array,
      required: true
    },
    videoProgress: {
      type: Number,
      default: 0
    }
  },
  methods: {
    getStepClass(status) {
      switch (status) {
        case 'processing':
          return 'bg-blue-500 pulse-glow energy-wave'
        case 'complete':
          return 'bg-green-500 energy-wave'
        case 'waiting':
        default:
          return 'bg-gray-600'
      }
    },
    getTextClass(status) {
      switch (status) {
        case 'processing':
        case 'complete':
          return 'text-white'
        case 'waiting':
        default:
          return 'text-gray-500'
      }
    },
    getTimeClass(status) {
      switch (status) {
        case 'processing':
        case 'complete':
          return 'text-gray-400'
        case 'waiting':
        default:
          return 'text-gray-500'
      }
    }
  }
}
</script>

<style scoped>
.card-glow {
  background: rgba(26, 31, 46, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.4); }
  to { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8); }
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.progress-bar {
  background: linear-gradient(90deg, #0066ff, #00d4ff);
  transition: width 0.5s ease;
}
</style>
