<template>
  <div class="detection-result rounded-2xl p-4 card-hover">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-lg font-bold text-white">深伪检测结果</h3>
      <i class="fas fa-search text-xl text-red-400 floating-icon energy-wave"></i>
    </div>
    <div class="text-center">
      <div v-if="!detectionResult" class="text-3xl mb-3">
        <i class="fas fa-hourglass-half text-yellow-400 pulse-glow"></i>
      </div>
      <div v-else class="text-3xl mb-3">
        <i :class="resultIconClass"></i>
      </div>
      
      <p v-if="!detectionResult" class="text-gray-400 text-sm">
        等待检测完成...
      </p>
      <div v-else class="transition-all duration-500">
        <div class="text-2xl font-bold mb-2 pulse-glow" :class="scoreColorClass">
          {{ detectionResult.score }}%
        </div>
        <div class="text-sm text-gray-400">
          {{ detectionResult.label }}
        </div>
        <div v-if="detectionResult.riskLevel" class="mt-2">
          <span
            class="px-2 py-1 rounded text-xs font-medium energy-wave"
            :class="riskLevelClass"
          >
            {{ detectionResult.riskLevel }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DetectionResults',
  props: {
    detectionResult: {
      type: Object,
      default: null
    }
  },
  computed: {
    resultIconClass() {
      if (!this.detectionResult) return ''
      
      const score = parseFloat(this.detectionResult.score)
      if (score >= 80) {
        return 'fas fa-exclamation-triangle text-red-400'
      } else if (score >= 50) {
        return 'fas fa-exclamation-circle text-yellow-400'
      } else {
        return 'fas fa-check-circle text-green-400'
      }
    },
    scoreColorClass() {
      if (!this.detectionResult) return ''
      
      const score = parseFloat(this.detectionResult.score)
      if (score >= 80) {
        return 'text-red-400'
      } else if (score >= 50) {
        return 'text-yellow-400'
      } else {
        return 'text-green-400'
      }
    },
    riskLevelClass() {
      if (!this.detectionResult?.riskLevel) return ''
      
      switch (this.detectionResult.riskLevel) {
        case '高风险':
          return 'bg-red-600 text-white'
        case '中风险':
          return 'bg-yellow-600 text-white'
        case '低风险':
          return 'bg-green-600 text-white'
        default:
          return 'bg-gray-600 text-white'
      }
    }
  }
}
</script>

<style scoped>
.detection-result {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.4); }
  to { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8); }
}
</style>
