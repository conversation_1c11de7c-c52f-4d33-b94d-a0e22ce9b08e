<template>
  <div class="ai-explanation rounded-2xl p-6 flex-1 card-hover" style="min-height: 400px;">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-bold text-white data-flow">
        <i class="fas fa-brain mr-2 text-cyan-400 floating-icon"></i>
        大模型AI分析
      </h3>
      <div class="flex items-center space-x-2">
        <div :class="['status-dot', `status-${analysis.status}`, analysis.status === 'processing' ? 'status-pulse' : '']"></div>
        <span class="text-sm text-gray-400">{{ analysis.text }}</span>
      </div>
    </div>
    <div class="bg-gray-800 bg-opacity-50 rounded-xl p-4 h-80 overflow-y-auto circuit-bg">
      <p
        ref="analysisContent"
        class="text-sm leading-relaxed transition-all duration-300"
        :class="[contentClass, { 'typewriter': typewriterActive && analysis.status === 'processing' }]"
      >
        {{ displayContent }}
      </p>
    </div>
  </div>
</template>

<script>
import { ref, watch, nextTick, computed } from 'vue'

export default {
  name: 'AIAnalysis',
  props: {
    analysis: {
      type: Object,
      required: true,
      default: () => ({
        status: 'waiting',
        text: '等待分析',
        content: ''
      })
    }
  },
  setup(props) {
    const analysisContent = ref(null)
    const displayContent = ref('')
    const typewriterActive = ref(false)

    // 默认分析内容
    const defaultContent = `检测分析将在视频生成完成后开始，AI将从多个维度分析视频的真实性，并提供详细的技术解释...

分析维度包括：
• 面部特征一致性检测
• 光照与阴影合理性分析
• 边缘模糊度与自然度评估
• 时序连贯性与运动轨迹分析
• 纹理细节与皮肤质感检测
• 眼部运动与眨眼频率分析
• 嘴唇同步性与发音匹配度
• 整体视觉一致性评估

AI模型将综合以上多个维度的检测结果，给出最终的真实性判断和置信度评分。`

    // 打字机效果
    const typewriterEffect = (text) => {
      typewriterActive.value = true
      displayContent.value = ''
      let i = 0
      
      const typeInterval = setInterval(() => {
        if (i < text.length) {
          displayContent.value += text.charAt(i)
          i++
          // 自动滚动到底部
          nextTick(() => {
            if (analysisContent.value?.parentElement) {
              analysisContent.value.parentElement.scrollTop = 
                analysisContent.value.parentElement.scrollHeight
            }
          })
        } else {
          clearInterval(typeInterval)
          typewriterActive.value = false
        }
      }, 20)
    }

    // 监听分析内容变化
    watch(
      () => props.analysis.content,
      (newContent) => {
        if (newContent && newContent.trim() !== '') {
          typewriterEffect(newContent)
        } else {
          displayContent.value = defaultContent
          typewriterActive.value = false
        }
      },
      { immediate: true }
    )

    // 监听分析状态变化
    watch(
      () => props.analysis.status,
      (newStatus) => {
        if (newStatus === 'processing' && !props.analysis.content) {
          displayContent.value = '正在进行AI分析，请稍候...'
          typewriterActive.value = false
        }
      }
    )

    const contentClass = computed(() => {
      if (props.analysis.status === 'complete' && props.analysis.content) {
        return 'text-cyan-300'
      }
      return 'text-gray-400'
    })

    return {
      analysisContent,
      displayContent,
      typewriterActive,
      contentClass
    }
  }
}
</script>

<style scoped>
.ai-explanation {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-waiting { background-color: #fbbf24; }
.status-processing { 
  background-color: #0066ff; 
  animation: pulse 1.5s infinite;
}
.status-complete { background-color: #10b981; }
.status-error { background-color: #ef4444; }

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}
</style>
