<template>
  <div class="text-white">
    <!-- Header -->
    <header class="px-8 py-6 border-b border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center">
            <i class="fas fa-shield-alt text-xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">SSID - Defake深伪检测防御演示</h1>
            <p class="text-gray-400">实时展示人工智能内容安全技术</p>
          </div>
        </div>
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div :class="['status-dot', `status-${demoController.demoStatus.type}`]"></div>
              <span class="text-gray-400 font-medium">{{ demoController.demoStatus.text }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <i :class="['fas fa-mobile-alt text-lg', demoController.appStatus.connected ? 'text-green-400' : 'text-gray-400']"></i>
              <span :class="['text-sm', demoController.appStatus.connected ? 'text-green-400' : 'text-gray-400']">
                {{ demoController.appStatus.text }}
              </span>
            </div>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-white">{{ currentTime }}</div>
            <div class="text-sm text-gray-400">当前时间</div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="px-8 py-6">
      <!-- Main Layout Grid -->
      <div class="grid grid-cols-12 gap-8 main-container">
        <!-- Left Column - Progress Timeline -->
        <div class="col-span-3 space-y-6">
          <ProgressTimeline
            :steps="demoController.progressSteps"
            :video-progress="demoController.demoData.videoProgress"
          />
        </div>

        <!-- Center Column - Original Photo & Video Comparison -->
        <div class="col-span-5 flex flex-col">
          <MediaComparison
            :original-photo="demoController.demoData.originalPhoto"
            :generated-video="demoController.demoData.generatedVideo"
            :video-progress="demoController.demoData.videoProgress"
            :app-connected="demoController.appStatus.connected"
            :demo-status="demoController.demoStatus.type"
            @start-demo="startNewDemo"
            @enable-upload="enablePhotoUpload"
          />
        </div>

        <!-- Right Column - Detection Results & AI Analysis -->
        <div class="col-span-4 space-y-4">
          <DetectionResults :detection-result="demoController.demoData.detectionResult" />
          <AIAnalysis :analysis="demoController.demoData.aiAnalysis" />
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import demoController from './services/demoController.js'
import websocketService from './services/websocket.js'
import ProgressTimeline from './components/ProgressTimeline.vue'
import MediaComparison from './components/MediaComparison.vue'
import DetectionResults from './components/DetectionResults.vue'
import AIAnalysis from './components/AIAnalysis.vue'

export default {
  name: 'App',
  components: {
    ProgressTimeline,
    MediaComparison,
    DetectionResults,
    AIAnalysis
  },
  setup() {
    // 响应式数据
    const currentTime = ref('')

    // 时间更新
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    // 开始新演示
    const startNewDemo = () => {
      demoController.startNewDemo()
    }

    // 启用拍照上传
    const enablePhotoUpload = () => {
      demoController.enablePhotoUpload()
    }

    // 生命周期钩子
    onMounted(() => {
      // 启动时间更新
      updateTime()
      const timeInterval = setInterval(updateTime, 1000)

      // 连接WebSocket
      websocketService.connect()

      // 清理函数
      onUnmounted(() => {
        clearInterval(timeInterval)
        websocketService.disconnect()
      })
    })

    return {
      currentTime,
      demoController,
      startNewDemo,
      enablePhotoUpload
    }
  }
}
</script>

<style scoped>
.main-container {
  height: calc(100vh - 120px);
}

.gradient-text {
  background: linear-gradient(135deg, #00d4ff, #0066ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-waiting { background-color: #fbbf24; }
.status-processing { 
  background-color: #0066ff; 
  animation: pulse 1.5s infinite;
}
.status-complete { background-color: #10b981; }
.status-error { background-color: #ef4444; }

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}
</style>
