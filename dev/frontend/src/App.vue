<template>
  <div class="text-white min-h-screen">
    <!-- Header -->
    <header class="px-8 py-6 border-b border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center">
            <i class="fas fa-shield-alt text-xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">SSID - Defake深伪检测防御演示</h1>
            <p class="text-gray-400">实时展示人工智能内容安全技术</p>
          </div>
        </div>
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div :class="['status-dot', `status-${demoStatus.type}`]"></div>
              <span class="text-gray-400 font-medium">{{ demoStatus.text }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <i :class="['fas fa-mobile-alt text-lg', appStatus.connected ? 'text-green-400' : 'text-gray-400']"></i>
              <span :class="['text-sm', appStatus.connected ? 'text-green-400' : 'text-gray-400']">
                {{ appStatus.text }}
              </span>
            </div>
          </div>
          <div class="text-right">
            <div class="text-2xl font-bold text-white">{{ currentTime }}</div>
            <div class="text-sm text-gray-400">当前时间</div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="px-8 py-6">
      <div class="grid grid-cols-12 gap-8" style="height: calc(100vh - 120px);">
        <!-- Left Column - Progress Timeline -->
        <div class="col-span-3 space-y-6">
          <div class="card-glow rounded-2xl p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold text-white">
                <i class="fas fa-timeline mr-2 text-orange-400"></i>
                处理进度
              </h3>
              <i class="fas fa-cogs text-2xl text-purple-400 floating-icon"></i>
            </div>
            <div class="space-y-4">
              <div v-for="(step, index) in progressSteps" :key="index" class="flex items-center space-x-3">
                <div :class="['w-6 h-6 rounded-full flex items-center justify-center', getStepClass(step.status)]">
                  <i v-if="step.status === 'complete'" class="fas fa-check text-xs"></i>
                  <i v-else :class="`fas fa-${step.icon} text-xs`"></i>
                </div>
                <div class="flex-1">
                  <div :class="['font-medium text-sm', getTextClass(step.status)]">{{ step.name }}</div>
                  <div :class="['text-xs', getTimeClass(step.status)]">{{ step.time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Center Column -->
        <div class="col-span-5 flex flex-col">
          <div class="card-glow rounded-2xl p-6 flex-1">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-bold text-white">
                <i class="fas fa-exchange-alt mr-2 text-green-400"></i>
                原图与合成视频对比
              </h3>
              <div class="flex items-center space-x-4">
                <button @click="startDemo" class="px-6 py-3 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors text-sm font-medium">
                  <i class="fas fa-redo mr-2"></i>开始新演示
                </button>
              </div>
            </div>
            <div class="flex justify-center items-center h-full space-x-8">
              <div class="flex flex-col items-center">
                <h4 class="text-lg font-medium text-gray-300 mb-3">原始照片</h4>
                <div class="bg-gray-800 rounded-xl flex items-center justify-center" style="width: 240px; height: 426px;">
                  <div class="text-center text-gray-400">
                    <i class="fas fa-mobile-alt text-4xl mb-4"></i>
                    <p class="text-base mb-2">{{ photoStatus.title }}</p>
                    <p class="text-sm">{{ photoStatus.subtitle }}</p>
                  </div>
                </div>
              </div>
              <div class="flex flex-col items-center">
                <i class="fas fa-arrow-right text-3xl text-cyan-400 floating-icon"></i>
                <span class="text-sm text-gray-400 mt-3 font-medium">AI生成</span>
              </div>
              <div class="flex flex-col items-center">
                <h4 class="text-lg font-medium text-gray-300 mb-3">合成视频</h4>
                <div class="video-container rounded-xl flex items-center justify-center" style="width: 240px; height: 426px;">
                  <div class="text-center text-gray-400">
                    <i class="fas fa-video text-4xl mb-4"></i>
                    <p class="text-base mb-2">等待视频生成</p>
                    <p class="text-sm mb-4">需要先完成拍照上传</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="col-span-4 space-y-4">
          <div class="detection-result rounded-2xl p-4">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-bold text-white">深伪检测结果</h3>
              <i class="fas fa-search text-xl text-red-400 floating-icon"></i>
            </div>
            <div class="text-center">
              <div class="text-3xl mb-3">
                <i class="fas fa-hourglass-half text-yellow-400 pulse-glow"></i>
              </div>
              <p class="text-gray-400 text-sm">等待检测完成...</p>
            </div>
          </div>

          <div class="ai-explanation rounded-2xl p-6 flex-1" style="min-height: 400px;">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-xl font-bold text-white">
                <i class="fas fa-brain mr-2 text-cyan-400"></i>
                大模型AI分析
              </h3>
              <div class="flex items-center space-x-2">
                <div class="status-dot status-waiting"></div>
                <span class="text-sm text-gray-400">等待分析</span>
              </div>
            </div>
            <div class="bg-gray-800 bg-opacity-50 rounded-xl p-4 h-80 overflow-y-auto">
              <p class="text-gray-400 text-sm leading-relaxed">
                检测分析将在视频生成完成后开始，AI将从多个维度分析视频的真实性，并提供详细的技术解释...
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'App',
  setup() {
    const currentTime = ref('')
    const demoStatus = ref({ type: 'waiting', text: '系统就绪' })
    const appStatus = ref({ connected: false, text: 'APP未连接' })
    const photoStatus = ref({ title: '等待APP连接', subtitle: '正在连接手机APP...' })
    
    const progressSteps = ref([
      { name: 'APP连接', status: 'waiting', time: '未连接', icon: 'mobile-alt' },
      { name: '用户拍照', status: 'waiting', time: '等待中', icon: 'camera' },
      { name: '人脸检测', status: 'waiting', time: '等待中', icon: 'search' },
      { name: '视频生成', status: 'waiting', time: '等待中', icon: 'cog' },
      { name: '深伪检测', status: 'waiting', time: '等待中', icon: 'shield-alt' },
      { name: 'AI分析', status: 'waiting', time: '等待中', icon: 'brain' }
    ])

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const getStepClass = (status) => {
      switch (status) {
        case 'processing': return 'bg-blue-500 pulse-glow'
        case 'complete': return 'bg-green-500'
        default: return 'bg-gray-600'
      }
    }

    const getTextClass = (status) => {
      return status === 'waiting' ? 'text-gray-500' : 'text-white'
    }

    const getTimeClass = (status) => {
      return status === 'waiting' ? 'text-gray-500' : 'text-gray-400'
    }

    const startDemo = () => {
      console.log('开始演示')
      demoStatus.value = { type: 'processing', text: '演示进行中' }
      
      // 模拟APP连接
      setTimeout(() => {
        appStatus.value = { connected: true, text: 'APP已连接' }
        progressSteps.value[0].status = 'complete'
        progressSteps.value[0].time = new Date().toLocaleTimeString('zh-CN', { hour12: false })
        photoStatus.value = { title: 'APP已连接', subtitle: '请使用手机APP拍照' }
      }, 2000)
    }

    onMounted(() => {
      updateTime()
      setInterval(updateTime, 1000)
      
      // 模拟初始APP连接
      setTimeout(() => {
        appStatus.value = { connected: true, text: 'APP已连接' }
        progressSteps.value[0].status = 'complete'
        progressSteps.value[0].time = new Date().toLocaleTimeString('zh-CN', { hour12: false })
        photoStatus.value = { title: 'APP已连接', subtitle: '点击"开始新演示"开始' }
        demoStatus.value = { type: 'waiting', text: '系统就绪，可开始演示' }
      }, 3000)
    })

    return {
      currentTime,
      demoStatus,
      appStatus,
      photoStatus,
      progressSteps,
      startDemo,
      getStepClass,
      getTextClass,
      getTimeClass
    }
  }
}
</script>

<style scoped>
.main-container {
  height: calc(100vh - 120px);
}

.gradient-text {
  background: linear-gradient(135deg, #00d4ff, #0066ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-waiting { background-color: #fbbf24; }
.status-processing { 
  background-color: #0066ff; 
  animation: pulse 1.5s infinite;
}
.status-complete { background-color: #10b981; }

.card-glow {
  background: rgba(26, 31, 46, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-container {
  background: radial-gradient(circle at center, #1e293b 0%, #0f172a 100%);
  border: 2px solid #334155;
}

.detection-result {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.ai-explanation {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.4); }
  to { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}
</style>
