<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID - Defake深伪检测防御演示</title>

    <!-- External CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <style>
        * {
            font-family: 'Noto Sans SC', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f172a 100%);
            overflow: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* 背景电路板效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px),
                linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: circuitMove 30s linear infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes circuitMove {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }

        #app {
            position: relative;
            z-index: 1;
        }

        .main-container {
            height: calc(100vh - 120px);
        }

        .gradient-text {
            background: linear-gradient(135deg, #00d4ff, #0066ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-waiting { background-color: #fbbf24; }
        .status-processing {
            background-color: #0066ff;
            animation: pulse 1.5s infinite;
        }
        .status-complete { background-color: #10b981; }

        .card-glow {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .video-container {
            background: radial-gradient(circle at center, #1e293b 0%, #0f172a 100%);
            border: 2px solid #334155;
        }

        .detection-result {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .ai-explanation {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
            border: 1px solid rgba(6, 182, 212, 0.3);
        }

        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }

        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.4); }
            to { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .btn-click {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-click:active {
            transform: scale(0.98);
        }

        .energy-wave {
            animation: energyWave 2s infinite;
        }

        @keyframes energyWave {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
                transform: scale(1);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
                transform: scale(1);
            }
        }

        .progress-bar {
            background: linear-gradient(90deg, #0066ff, #00d4ff);
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="text-white min-h-screen">
            <!-- Header -->
            <header class="px-8 py-6 border-b border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center">
                            <i class="fas fa-shield-alt text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold gradient-text">SSID - Defake深伪检测防御演示</h1>
                            <p class="text-gray-400">实时展示人工智能内容安全技术</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div :class="['status-dot', `status-${demoStatus.type}`]"></div>
                                <span class="text-gray-400 font-medium">{{ demoStatus.text }}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i :class="['fas fa-mobile-alt text-lg', appStatus.connected ? 'text-green-400' : 'text-gray-400']"></i>
                                <span :class="['text-sm', appStatus.connected ? 'text-green-400' : 'text-gray-400']">
                                    {{ appStatus.text }}
                                </span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-white">{{ currentTime }}</div>
                            <div class="text-sm text-gray-400">当前时间</div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="px-8 py-6">
                <div class="grid grid-cols-12 gap-8 main-container">
                    <!-- Left Column - Progress Timeline -->
                    <div class="col-span-3 space-y-6">
                        <div class="card-glow rounded-2xl p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-bold text-white">
                                    <i class="fas fa-timeline mr-2 text-orange-400"></i>
                                    处理进度
                                </h3>
                                <i class="fas fa-cogs text-2xl text-purple-400 floating-icon energy-wave"></i>
                            </div>
                            <div class="space-y-4">
                                <div v-for="(step, index) in progressSteps" :key="index" class="flex items-center space-x-3">
                                    <div :class="['w-6 h-6 rounded-full flex items-center justify-center transition-all duration-500', getStepClass(step.status)]">
                                        <i v-if="step.status === 'complete'" class="fas fa-check text-xs"></i>
                                        <i v-else :class="`fas fa-${step.icon} text-xs`"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div :class="['font-medium text-sm', getTextClass(step.status)]">{{ step.name }}</div>
                                        <div :class="['text-xs', getTimeClass(step.status)]">{{ step.time }}</div>
                                        <!-- 视频生成进度条 -->
                                        <div v-if="step.name.includes('视频生成') && step.status === 'processing'" class="flex items-center mt-1">
                                            <div class="w-16 bg-gray-600 rounded-full h-2 mr-2 relative overflow-hidden">
                                                <div class="progress-bar h-2 rounded-full" :style="`width: ${videoProgress}%`"></div>
                                            </div>
                                            <span class="text-gray-500 text-xs">{{ videoProgress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Center Column -->
                    <div class="col-span-5 flex flex-col">
                        <div class="card-glow rounded-2xl p-6 flex-1">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-bold text-white">
                                    <i class="fas fa-exchange-alt mr-2 text-green-400"></i>
                                    原图与合成视频对比
                                </h3>
                                <div class="flex items-center space-x-4">
                                    <button @click="startDemo" class="px-6 py-3 bg-orange-600 hover:bg-orange-700 rounded-lg transition-all duration-300 text-sm font-medium btn-click energy-wave">
                                        <i class="fas fa-redo mr-2"></i>{{ resetButtonText }}
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-center items-center h-full space-x-8">
                                <!-- 原始照片 -->
                                <div class="flex flex-col items-center">
                                    <h4 class="text-lg font-medium text-gray-300 mb-3">原始照片</h4>
                                    <div class="bg-gray-800 rounded-xl flex items-center justify-center relative overflow-hidden"
                                         style="width: 240px; height: 426px;"
                                         :class="{ 'pulse-glow': !originalPhoto && appStatus.connected }">
                                        <div v-if="!originalPhoto" class="text-center text-gray-400">
                                            <i :class="photoIconClass" class="text-4xl mb-4 floating-icon"></i>
                                            <p class="text-base mb-2">{{ photoStatus.title }}</p>
                                            <p class="text-sm">{{ photoStatus.subtitle }}</p>
                                        </div>
                                        <img v-else :src="originalPhoto" class="w-full h-full object-cover rounded-xl transition-all duration-500" alt="原始照片" />
                                        <div v-if="originalPhoto" class="absolute top-2 right-2">
                                            <div class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                                                <i class="fas fa-check mr-1"></i>已上传
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 箭头指示 -->
                                <div class="flex flex-col items-center">
                                    <i class="fas fa-arrow-right text-3xl text-cyan-400 floating-icon"></i>
                                    <span class="text-sm text-gray-400 mt-3 font-medium">AI生成</span>
                                </div>

                                <!-- 合成视频 -->
                                <div class="flex flex-col items-center">
                                    <h4 class="text-lg font-medium text-gray-300 mb-3">合成视频</h4>
                                    <div class="video-container rounded-xl flex items-center justify-center relative overflow-hidden"
                                         style="width: 240px; height: 426px;"
                                         :class="{ 'hologram-effect': generatedVideo }">
                                        <div v-if="!generatedVideo" class="text-center text-gray-400">
                                            <i :class="videoIconClass" class="text-4xl mb-4 floating-icon"></i>
                                            <p class="text-base mb-2">{{ videoStatusText.title }}</p>
                                            <p class="text-sm mb-4">{{ videoStatusText.subtitle }}</p>
                                            <div v-if="showVideoProgress" class="w-full px-4">
                                                <div class="w-full bg-gray-600 rounded-full h-2 mb-2 relative overflow-hidden">
                                                    <div class="progress-bar h-2 rounded-full transition-all duration-500" :style="`width: ${videoProgress}%`"></div>
                                                </div>
                                                <span class="text-xs text-gray-400">{{ videoProgress }}%</span>
                                            </div>
                                        </div>
                                        <video v-else :src="generatedVideo" class="w-full h-full object-cover rounded-xl transition-all duration-500" controls autoplay loop></video>
                                        <div v-if="generatedVideo" class="absolute top-2 left-2">
                                            <div class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium pulse-glow">
                                                <i class="fas fa-play mr-1"></i>AI生成
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="col-span-4 space-y-4">
                        <!-- Detection Results -->
                        <div class="detection-result rounded-2xl p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-bold text-white">深伪检测结果</h3>
                                <i class="fas fa-search text-xl text-red-400 floating-icon energy-wave"></i>
                            </div>
                            <div class="text-center">
                                <div v-if="!detectionResult" class="text-3xl mb-3">
                                    <i class="fas fa-hourglass-half text-yellow-400 pulse-glow"></i>
                                </div>
                                <div v-else class="text-3xl mb-3">
                                    <i :class="resultIconClass"></i>
                                </div>

                                <p v-if="!detectionResult" class="text-gray-400 text-sm">
                                    等待检测完成...
                                </p>
                                <div v-else class="transition-all duration-500">
                                    <div class="text-2xl font-bold mb-2 pulse-glow" :class="scoreColorClass">
                                        {{ detectionResult.score }}%
                                    </div>
                                    <div class="text-sm text-gray-400">
                                        {{ detectionResult.label }}
                                    </div>
                                    <div v-if="detectionResult.riskLevel" class="mt-2">
                                        <span class="px-2 py-1 rounded text-xs font-medium energy-wave" :class="riskLevelClass">
                                            {{ detectionResult.riskLevel }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Analysis -->
                        <div class="ai-explanation rounded-2xl p-6 flex-1" style="min-height: 400px;">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-bold text-white">
                                    <i class="fas fa-brain mr-2 text-cyan-400 floating-icon"></i>
                                    大模型AI分析
                                </h3>
                                <div class="flex items-center space-x-2">
                                    <div :class="['status-dot', `status-${aiAnalysis.status}`, aiAnalysis.status === 'processing' ? 'status-pulse' : '']"></div>
                                    <span class="text-sm text-gray-400">{{ aiAnalysis.text }}</span>
                                </div>
                            </div>
                            <div class="bg-gray-800 bg-opacity-50 rounded-xl p-4 h-80 overflow-y-auto">
                                <p class="text-sm leading-relaxed transition-all duration-300" :class="contentClass">
                                    {{ displayContent }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted, onUnmounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const currentTime = ref('')
                const demoStatus = ref({ type: 'waiting', text: '系统就绪' })
                const appStatus = ref({ connected: false, text: 'APP未连接' })
                const originalPhoto = ref(null)
                const generatedVideo = ref(null)
                const videoProgress = ref(0)
                const detectionResult = ref(null)
                const aiAnalysis = ref({ status: 'waiting', text: '等待分析', content: '' })
                const displayContent = ref('')
                const isRunning = ref(false)
                const currentStep = ref(0)

                const progressSteps = ref([
                    { name: 'APP连接', status: 'waiting', time: '未连接', icon: 'mobile-alt' },
                    { name: '用户拍照', status: 'waiting', time: '等待中', icon: 'camera' },
                    { name: '人脸检测', status: 'waiting', time: '等待中', icon: 'search' },
                    { name: '视频生成', status: 'waiting', time: '等待中', icon: 'cog' },
                    { name: '深伪检测', status: 'waiting', time: '等待中', icon: 'shield-alt' },
                    { name: 'AI分析', status: 'waiting', time: '等待中', icon: 'brain' }
                ])

                // 默认分析内容
                const defaultContent = `检测分析将在视频生成完成后开始，AI将从多个维度分析视频的真实性，并提供详细的技术解释...

分析维度包括：
• 面部特征一致性检测
• 光照与阴影合理性分析
• 边缘模糊度与自然度评估
• 时序连贯性与运动轨迹分析
• 纹理细节与皮肤质感检测
• 眼部运动与眨眼频率分析
• 嘴唇同步性与发音匹配度
• 整体视觉一致性评估

AI模型将综合以上多个维度的检测结果，给出最终的真实性判断和置信度评分。`

                // 计算属性
                const resetButtonText = computed(() => '开始新演示')

                const photoIconClass = computed(() => {
                    return appStatus.value.connected ? 'fas fa-mobile-alt text-green-400' : 'fas fa-mobile-alt'
                })

                const photoStatus = computed(() => {
                    if (!appStatus.value.connected) {
                        return { title: '等待APP连接', subtitle: '正在连接手机APP...' }
                    }
                    if (originalPhoto.value) {
                        return { title: '照片上传成功', subtitle: '开始处理...' }
                    }
                    if (demoStatus.value.type === 'waiting') {
                        return { title: 'APP已连接', subtitle: '点击"开始新演示"开始' }
                    }
                    return { title: '拍照功能已开启', subtitle: '请使用手机APP拍照' }
                })

                const videoIconClass = computed(() => {
                    if (generatedVideo.value) return 'fas fa-check-circle text-green-400'
                    if (showVideoProgress.value) return 'fas fa-cog text-blue-400 fa-spin'
                    return 'fas fa-video'
                })

                const videoStatusText = computed(() => {
                    if (generatedVideo.value) {
                        return { title: '视频生成完成', subtitle: '点击播放查看' }
                    }
                    if (showVideoProgress.value) {
                        return { title: '正在生成视频', subtitle: '请稍候...' }
                    }
                    return { title: '等待视频生成', subtitle: '需要先完成拍照上传' }
                })

                const showVideoProgress = computed(() => {
                    return videoProgress.value > 0 && !generatedVideo.value
                })

                const resultIconClass = computed(() => {
                    if (!detectionResult.value) return ''
                    const score = parseFloat(detectionResult.value.score)
                    if (score >= 80) return 'fas fa-exclamation-triangle text-red-400'
                    if (score >= 50) return 'fas fa-exclamation-circle text-yellow-400'
                    return 'fas fa-check-circle text-green-400'
                })

                const scoreColorClass = computed(() => {
                    if (!detectionResult.value) return ''
                    const score = parseFloat(detectionResult.value.score)
                    if (score >= 80) return 'text-red-400'
                    if (score >= 50) return 'text-yellow-400'
                    return 'text-green-400'
                })

                const riskLevelClass = computed(() => {
                    if (!detectionResult.value?.riskLevel) return ''
                    switch (detectionResult.value.riskLevel) {
                        case '高风险': return 'bg-red-600 text-white'
                        case '中风险': return 'bg-yellow-600 text-white'
                        case '低风险': return 'bg-green-600 text-white'
                        default: return 'bg-gray-600 text-white'
                    }
                })

                const contentClass = computed(() => {
                    if (aiAnalysis.value.status === 'complete' && aiAnalysis.value.content) {
                        return 'text-cyan-300'
                    }
                    return 'text-gray-400'
                })

                // 方法
                const updateTime = () => {
                    const now = new Date()
                    currentTime.value = now.toLocaleTimeString('zh-CN', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    })
                }

                const getStepClass = (status) => {
                    switch (status) {
                        case 'processing': return 'bg-blue-500 pulse-glow energy-wave'
                        case 'complete': return 'bg-green-500 energy-wave'
                        default: return 'bg-gray-600'
                    }
                }

                const getTextClass = (status) => {
                    return status === 'waiting' ? 'text-gray-500' : 'text-white'
                }

                const getTimeClass = (status) => {
                    return status === 'waiting' ? 'text-gray-500' : 'text-gray-400'
                }

                const updateProgressStep = (stepIndex, status, customText = null) => {
                    const step = progressSteps.value[stepIndex]
                    if (!step) return

                    step.status = status
                    if (customText) step.name = customText

                    if (status === 'complete') {
                        const now = new Date()
                        step.time = now.toLocaleTimeString('zh-CN', { hour12: false })
                    } else if (status === 'processing') {
                        step.time = '进行中...'
                    }
                }

                const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

                const startDemo = async () => {
                    console.log('开始新演示')
                    resetDemo()
                    demoStatus.value = { type: 'processing', text: '演示进行中' }

                    // 1.5秒后自动开启拍照功能
                    setTimeout(() => {
                        enablePhotoUpload()
                    }, 1500)
                }

                const enablePhotoUpload = () => {
                    if (!appStatus.value.connected) return

                    demoStatus.value = { type: 'waiting', text: '等待用户拍照上传' }
                    updateProgressStep(1, 'processing', '等待用户拍照')

                    // 模拟用户拍照上传（5秒后）
                    setTimeout(() => {
                        onPhotoUploaded()
                    }, 5000)
                }

                const onPhotoUploaded = async () => {
                    isRunning.value = true
                    demoStatus.value = { type: 'processing', text: '演示进行中' }
                    updateProgressStep(1, 'complete', '照片上传成功')

                    // 模拟显示照片
                    originalPhoto.value = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjQyNiIgdmlld0JveD0iMCAwIDI0MCA0MjYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyNDAiIGhlaWdodD0iNDI2IiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjEyMCIgeT0iMjEzIiBmaWxsPSIjOWNhM2FmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiPuWOn+Wni+eVp+eJhzwvdGV4dD4KPC9zdmc+'

                    // 开始后续处理流程
                    await executeStep(2) // 从人脸检测开始
                }

                const executeStep = async (stepIndex) => {
                    switch(stepIndex) {
                        case 2: // 人脸检测
                            updateProgressStep(2, 'processing', '人脸检测中')
                            await sleep(2000)
                            updateProgressStep(2, 'complete', '人脸检测完成')
                            await executeStep(3)
                            break
                        case 3: // 视频生成
                            updateProgressStep(3, 'processing', '视频生成中')
                            await simulateVideoGeneration()
                            updateProgressStep(3, 'complete', '视频生成完成')
                            await executeStep(4)
                            break
                        case 4: // 深伪检测
                            updateProgressStep(4, 'processing', '深伪检测中')
                            await sleep(3000)
                            detectionResult.value = {
                                score: '95.7',
                                label: '检测为合成内容',
                                riskLevel: '高风险'
                            }
                            updateProgressStep(4, 'complete', '检测完成')
                            await executeStep(5)
                            break
                        case 5: // AI分析
                            updateProgressStep(5, 'processing', 'AI分析中')
                            aiAnalysis.value.status = 'processing'
                            aiAnalysis.value.text = '分析中'
                            await simulateAIAnalysis()
                            updateProgressStep(5, 'complete', 'AI分析完成')
                            onDemoComplete()
                            break
                    }
                }

                const simulateVideoGeneration = async () => {
                    let progress = 0
                    while (progress < 100) {
                        progress += Math.random() * 8
                        if (progress > 100) progress = 100
                        videoProgress.value = Math.round(progress)
                        await sleep(500)
                    }

                    // 模拟显示视频
                    generatedVideo.value = 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAAr1tZGF0AAACrgYF//+q3EXpvebZSLeWLNgg2SPu73gyNjQgLSBjb3JlIDE1NSByMjkxNyAwYTg0ZDk4IC0gSC4yNjQvTVBFRy00IEFWQyBjb2RlYyAtIENvcHlsZWZ0IDIwMDMtMjAxOCAtIGh0dHA6Ly93d3cudmlkZW9sYW4ub3JnL3gyNjQuaHRtbCAtIG9wdGlvbnM6IGNhYmFjPTEgcmVmPTMgZGVibG9jaz0xOjA6MCBhbmFseXNlPTB4MzoweDExMyBtZT1oZXggc3VibWU9NyBwc3k9MSBwc3lfcmQ9MS4wMDowLjAwIG1peGVkX3JlZj0xIG1lX3JhbmdlPTE2IGNocm9tYV9tZT0xIHRyZWxsaXM9MSA4eDhkY3Q9MSBjcW09MCBkZWFkem9uZT0yMSwxMSBmYXN0X3Bza2lwPTEgY2hyb21hX3FwX29mZnNldD0tMiB0aHJlYWRzPTYgbG9va2FoZWFkX3RocmVhZHM9MSBzbGljZWRfdGhyZWFkcz0wIG5yPTAgZGVjaW1hdGU9MSBpbnRlcmxhY2VkPTAgYmx1cmF5X2NvbXBhdD0wIGNvbnN0cmFpbmVkX2ludHJhPTAgYmZyYW1lcz0zIGJfcHlyYW1pZD0yIGJfYWRhcHQ9MSBiX2JpYXM9MCBkaXJlY3Q9MSB3ZWlnaHRiPTEgb3Blbl9nb3A9MCB3ZWlnaHRwPTIga2V5aW50PTI1MCBrZXlpbnRfbWluPTI1IHNjZW5lY3V0PTQwIGludHJhX3JlZnJlc2g9MCByY19sb29rYWhlYWQ9NDAgcmM9Y3JmIG1idHJlZT0xIGNyZj0yMy4wIHFjb21wPTAuNjAgcXBtaW49MCBxcG1heD02OSBxcHN0ZXA9NCBpcF9yYXRpbz0xLjQwIGFxPTE6MS4wMACAAAABWWWIhAA3//728P4FNjuY0JcRzeidDNWXHu34++4L/Dz/8b8AAAMAAAMAAAMAAAMAAAMAVxYBjUOhp3EgAALvBXj8tL2W5iIQL1/DplVqCMhq+heILNt9dMtixtI+oxI2LAA='
                }

                const simulateAIAnalysis = async () => {
                    const analysisContent = `基于深度学习模型的综合分析结果：

经过多维度技术检测，该视频存在明显的人工合成特征。检测算法通过分析视频帧间的连续性、面部特征的自然度、以及整体视觉一致性等关键指标，确定该内容为AI生成的深伪视频。

【详细分析维度】

1. 面部特征一致性检测
   - 检测到面部轮廓与原始照片存在细微差异
   - 眼部区域的纹理细节不够自然
   - 鼻梁阴影与光照方向不完全匹配

2. 光照与阴影合理性分析
   - 面部光照分布存在不自然的渐变
   - 左侧脸颊阴影过于锐利，缺乏自然过渡
   - 眼窝阴影深度与环境光照不符

【最终判定】
置信度：95.7%
风险等级：高风险
建议处理：标记为合成媒体内容`

                    // 打字机效果
                    displayContent.value = ''
                    let i = 0
                    while (i < analysisContent.length) {
                        displayContent.value += analysisContent.charAt(i)
                        i++
                        await sleep(20)
                    }

                    aiAnalysis.value = {
                        status: 'complete',
                        text: '分析完成',
                        content: analysisContent
                    }
                }

                const onDemoComplete = () => {
                    isRunning.value = false
                    demoStatus.value = { type: 'complete', text: '演示完成' }

                    // 2分钟后自动重启
                    setTimeout(() => {
                        if (!isRunning.value) {
                            startDemo()
                        }
                    }, 120000)
                }

                const resetDemo = () => {
                    isRunning.value = false
                    currentStep.value = 0
                    originalPhoto.value = null
                    generatedVideo.value = null
                    videoProgress.value = 0
                    detectionResult.value = null
                    aiAnalysis.value = { status: 'waiting', text: '等待分析', content: '' }
                    displayContent.value = defaultContent

                    // 重置进度步骤
                    progressSteps.value.forEach((step, index) => {
                        if (index === 0 && appStatus.value.connected) {
                            step.status = 'complete'
                            step.name = 'APP连接成功'
                            step.time = new Date().toLocaleTimeString('zh-CN', { hour12: false })
                        } else {
                            step.status = 'waiting'
                            step.time = '等待中'
                            const originalNames = ['APP连接', '用户拍照', '人脸检测', '视频生成', '深伪检测', 'AI分析']
                            step.name = originalNames[index]
                        }
                    })
                }

                // 生命周期
                onMounted(() => {
                    updateTime()
                    setInterval(updateTime, 1000)
                    displayContent.value = defaultContent

                    // 模拟APP连接
                    setTimeout(() => {
                        appStatus.value = { connected: true, text: 'APP已连接' }
                        updateProgressStep(0, 'complete', 'APP连接成功')
                        demoStatus.value = { type: 'waiting', text: '系统就绪，可开始演示' }
                    }, 3000)
                })

                return {
                    currentTime,
                    demoStatus,
                    appStatus,
                    originalPhoto,
                    generatedVideo,
                    videoProgress,
                    detectionResult,
                    aiAnalysis,
                    displayContent,
                    progressSteps,
                    resetButtonText,
                    photoIconClass,
                    photoStatus,
                    videoIconClass,
                    videoStatusText,
                    showVideoProgress,
                    resultIconClass,
                    scoreColorClass,
                    riskLevelClass,
                    contentClass,
                    startDemo,
                    getStepClass,
                    getTextClass,
                    getTimeClass
                }
            }
        }).mount('#app')
    </script>
</body>
</html>