# WAIC Demo 前端项目

基于Vue 3 + Vite构建的WAIC深伪检测防御演示前端项目。

## 功能特性

- 🎯 **大屏展示界面**：科技感的三栏布局，适配大屏幕显示
- 🔄 **实时WebSocket通信**：与后端实时数据交换
- 📊 **6步进度时间线**：完整的演示流程状态管理
- 🖼️ **媒体对比展示**：原图与合成视频的对比显示
- 🛡️ **深伪检测结果**：实时显示检测结果和置信度
- 🧠 **AI分析展示**：打字机效果的详细分析内容
- ⚡ **自动化流程**：一键启动，自动循环演示

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 快速的前端构建工具
- **SockJS + STOMP** - WebSocket通信
- **Tailwind CSS** - 实用优先的CSS框架
- **Font Awesome** - 图标库

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 生产构建

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── ProgressTimeline.vue    # 进度时间线
│   ├── MediaComparison.vue     # 媒体对比
│   ├── DetectionResults.vue    # 检测结果
│   └── AIAnalysis.vue          # AI分析
├── services/           # 服务层
│   ├── websocket.js           # WebSocket通信服务
│   └── demoController.js      # 演示流程控制器
├── App.vue            # 主应用组件
└── main.js           # 应用入口
```

## WebSocket通信

### 连接配置

默认连接地址：`http://************:8701/ws`

可在 `src/services/websocket.js` 中修改连接地址。

### 消息格式

#### 接收消息（从后端）

```javascript
{
  "type": "upload",
  "imageBase64": "base64编码的图片数据",
  "videoBase64": "base64编码的视频数据"
}
```

#### 发送消息（到后端）

```javascript
{
  "type": "startDemo",  // 开始演示
}

{
  "type": "enableUpload"  // 启用拍照上传
}
```

## 演示流程

1. **系统启动** - 页面加载，自动连接WebSocket
2. **APP连接** - 模拟手机APP连接（2秒后自动完成）
3. **开始演示** - 点击"开始新演示"按钮
4. **自动开启拍照** - 1.5秒后自动开启拍照功能
5. **等待上传** - 等待用户使用手机APP拍照上传
6. **处理流程** - 人脸检测 → 视频生成 → 深伪检测 → AI分析
7. **结果展示** - 显示检测结果和AI分析内容
8. **自动循环** - 2分钟后自动开始新演示

## 自定义配置

### 修改WebSocket地址

编辑 `src/services/websocket.js`：

```javascript
connect(serverUrl = 'http://your-server:port/ws')
```

### 修改演示步骤时长

编辑 `src/services/demoController.js`：

```javascript
this.steps = [
  { name: 'faceDetection', duration: 2000 },    // 人脸检测时长
  { name: 'videoGeneration', duration: 15000 }, // 视频生成时长
  { name: 'deepfakeDetection', duration: 8000 }, // 深伪检测时长
  { name: 'aiExplanation', duration: 5000 }     // AI分析时长
]
```

### 修改自动重启时间

编辑 `src/services/demoController.js`：

```javascript
startAutoRestartCountdown() {
  this.countdownSeconds.value = 120 // 修改为所需秒数
}
```

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 开发说明

### 组件通信

- 使用Vue 3的Composition API
- 通过`demoController`单例管理全局状态
- 组件间通过props和events通信

### 状态管理

- `demoController.js` 负责整个演示流程的状态管理
- 使用Vue 3的`reactive`和`ref`实现响应式数据
- WebSocket消息自动更新到对应的状态

### 样式系统

- 基于Tailwind CSS的实用类
- 自定义CSS动画效果
- 响应式设计适配不同屏幕尺寸

## 故障排除

### WebSocket连接失败

1. 检查后端服务是否启动
2. 确认WebSocket地址是否正确
3. 检查网络连接和防火墙设置

### 页面显示异常

1. 清除浏览器缓存
2. 检查控制台错误信息
3. 确认所有依赖已正确安装

### 演示流程卡住

1. 点击"开始新演示"重置流程
2. 检查WebSocket连接状态
3. 查看控制台日志信息
