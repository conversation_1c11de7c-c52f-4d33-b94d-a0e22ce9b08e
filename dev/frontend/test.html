<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js 测试页面</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f172a 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .status {
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>🎯 WAIC Demo 测试页面</h1>
            <p>这是一个简单的Vue.js测试页面，用于验证基本功能是否正常。</p>
            
            <div class="status">
                <h2>当前状态</h2>
                <p>时间: {{ currentTime }}</p>
                <p>计数器: {{ counter }}</p>
                <p>演示状态: {{ demoStatus }}</p>
            </div>
            
            <div>
                <button @click="increment">增加计数</button>
                <button @click="startDemo" :class="{ pulse: isRunning }">
                    {{ isRunning ? '演示进行中...' : '开始演示' }}
                </button>
                <button @click="reset">重置</button>
            </div>
            
            <div v-if="isRunning" class="status">
                <h3>演示进度</h3>
                <div v-for="(step, index) in steps" :key="index">
                    <p :style="{ color: step.completed ? '#28a745' : '#6c757d' }">
                        {{ step.completed ? '✅' : '⏳' }} {{ step.name }}
                    </p>
                </div>
            </div>
            
            <div class="status">
                <h3>系统信息</h3>
                <p>Vue.js 版本: {{ vueVersion }}</p>
                <p>页面加载时间: {{ loadTime }}</p>
                <p>浏览器: {{ userAgent }}</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const currentTime = ref('')
                const counter = ref(0)
                const demoStatus = ref('就绪')
                const isRunning = ref(false)
                const loadTime = ref('')
                const vueVersion = ref(Vue.version || '3.x')
                const userAgent = ref(navigator.userAgent.split(' ')[0])
                
                const steps = ref([
                    { name: 'APP连接', completed: false },
                    { name: '用户拍照', completed: false },
                    { name: '人脸检测', completed: false },
                    { name: '视频生成', completed: false },
                    { name: '深伪检测', completed: false },
                    { name: 'AI分析', completed: false }
                ])

                const updateTime = () => {
                    const now = new Date()
                    currentTime.value = now.toLocaleTimeString('zh-CN')
                }

                const increment = () => {
                    counter.value++
                }

                const startDemo = async () => {
                    if (isRunning.value) return
                    
                    isRunning.value = true
                    demoStatus.value = '演示进行中'
                    
                    // 重置步骤
                    steps.value.forEach(step => step.completed = false)
                    
                    // 模拟演示流程
                    for (let i = 0; i < steps.value.length; i++) {
                        await new Promise(resolve => setTimeout(resolve, 1000))
                        steps.value[i].completed = true
                    }
                    
                    demoStatus.value = '演示完成'
                    isRunning.value = false
                }

                const reset = () => {
                    counter.value = 0
                    demoStatus.value = '就绪'
                    isRunning.value = false
                    steps.value.forEach(step => step.completed = false)
                }

                onMounted(() => {
                    loadTime.value = new Date().toLocaleTimeString('zh-CN')
                    updateTime()
                    setInterval(updateTime, 1000)
                })

                return {
                    currentTime,
                    counter,
                    demoStatus,
                    isRunning,
                    loadTime,
                    vueVersion,
                    userAgent,
                    steps,
                    increment,
                    startDemo,
                    reset
                }
            }
        }).mount('#app')
    </script>
</body>
</html>
