<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Examples Using STOMP Over WebSockets</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/bootstrap.min.responsive.css" rel="stylesheet">
    <style type="text/css">
      body { padding-top: 80px; }
    </style>
  </head>

  <body>
    
    <div class="navbar navbar-fixed-top">
      <div class="navbar-inner">
        <div class="container">
          <a class="brand" href="#">Examples Using Stomp Over WebSocket</a>
        </div>
      </div>
    </div>

    <div class="container-fluid">
      <div class="row-fluid">
        <p>The <a href="/chat/">Chat example</a> shows how to use <code>stomp.js</code> to send and receive STOMP messages directly from the Web browser.</p>
        <p>The <a href="/webworker/">WebWorker example</a> uses a WebWorker instead to send and receive STOMP messages from the broker.</p>
      </div>
    </div>
  </body>
</html>
