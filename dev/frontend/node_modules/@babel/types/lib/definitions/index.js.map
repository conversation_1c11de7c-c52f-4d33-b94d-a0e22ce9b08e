{"version": 3, "names": ["require", "_utils", "_placeholders", "_deprecatedAliases", "Object", "keys", "DEPRECATED_ALIASES", "for<PERSON>ach", "depre<PERSON><PERSON><PERSON><PERSON>", "FLIPPED_ALIAS_KEYS", "types", "set", "allExpandedTypes", "type", "aliases", "add", "TYPES", "exports", "concat", "VISITOR_KEYS", "DEPRECATED_KEYS"], "sources": ["../../src/definitions/index.ts"], "sourcesContent": ["import \"./core.ts\";\nimport \"./flow.ts\";\nimport \"./jsx.ts\";\nimport \"./misc.ts\";\nimport \"./experimental.ts\";\nimport \"./typescript.ts\";\nimport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n  allExpandedTypes,\n} from \"./utils.ts\";\nimport {\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n} from \"./placeholders.ts\";\nimport { DEPRECATED_ALIASES } from \"./deprecated-aliases.ts\";\n\n(\n  Object.keys(DEPRECATED_ALIASES) as (keyof typeof DEPRECATED_ALIASES)[]\n).forEach(deprecatedAlias => {\n  FLIPPED_ALIAS_KEYS[deprecatedAlias] =\n    FLIPPED_ALIAS_KEYS[DEPRECATED_ALIASES[deprecatedAlias]];\n});\n\nfor (const { types, set } of allExpandedTypes) {\n  for (const type of types) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      aliases.forEach(set.add, set);\n    } else {\n      set.add(type);\n    }\n  }\n}\n\nconst TYPES: Array<string> = [].concat(\n  Object.keys(VISITOR_KEYS),\n  Object.keys(FLIPPED_ALIAS_KEYS),\n  Object.keys(DEPRECATED_KEYS),\n);\n\nexport {\n  VISITOR_KEYS,\n  ALIAS_KEYS,\n  FLIPPED_ALIAS_KEYS,\n  NODE_FIELDS,\n  BUILDER_KEYS,\n  DEPRECATED_ALIASES,\n  DEPRECATED_KEYS,\n  NODE_PARENT_VALIDATIONS,\n  PLACEHOLDERS,\n  PLACEHOLDERS_ALIAS,\n  PLACEHOLDERS_FLIPPED_ALIAS,\n  TYPES,\n};\n\nexport type { FieldOptions } from \"./utils.ts\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAUA,IAAAE,aAAA,GAAAF,OAAA;AAKA,IAAAG,kBAAA,GAAAH,OAAA;AAGEI,MAAM,CAACC,IAAI,CAACC,qCAAkB,CAAC,CAC/BC,OAAO,CAACC,eAAe,IAAI;EAC3BC,yBAAkB,CAACD,eAAe,CAAC,GACjCC,yBAAkB,CAACH,qCAAkB,CAACE,eAAe,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF,KAAK,MAAM;EAAEE,KAAK;EAAEC;AAAI,CAAC,IAAIC,uBAAgB,EAAE;EAC7C,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACxB,MAAMI,OAAO,GAAGL,yBAAkB,CAACI,IAAI,CAAC;IACxC,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACP,OAAO,CAACI,GAAG,CAACI,GAAG,EAAEJ,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLA,GAAG,CAACI,GAAG,CAACF,IAAI,CAAC;IACf;EACF;AACF;AAEA,MAAMG,KAAoB,GAAAC,OAAA,CAAAD,KAAA,GAAG,EAAE,CAACE,MAAM,CACpCd,MAAM,CAACC,IAAI,CAACc,mBAAY,CAAC,EACzBf,MAAM,CAACC,IAAI,CAACI,yBAAkB,CAAC,EAC/BL,MAAM,CAACC,IAAI,CAACe,sBAAe,CAC7B,CAAC", "ignoreList": []}