{"name": "eventsource", "version": "2.0.2", "description": "W3C compliant EventSource client for Node.js and browser (polyfill)", "keywords": ["eventsource", "http", "streaming", "sse", "polyfill"], "homepage": "http://github.com/EventSource/eventsource", "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/EventSource/eventsource.git"}, "bugs": {"url": "http://github.com/EventSource/eventsource/issues"}, "directories": {"lib": "./lib"}, "main": "./lib/eventsource", "license": "MIT", "licenses": [{"type": "MIT", "url": "http://github.com/EventSource/eventsource/raw/master/LICENSE"}], "devDependencies": {"buffer-from": "^1.1.1", "express": "^4.15.3", "mocha": "^3.5.3", "nyc": "^11.2.1", "serve-static": "^1.12.3", "ssestream": "^1.0.0", "standard": "^10.0.2", "webpack": "^3.5.6"}, "scripts": {"test": "mocha --reporter spec && standard", "polyfill": "webpack lib/eventsource-polyfill.js example/eventsource-polyfill.js", "postpublish": "git push && git push --tags", "coverage": "nyc --reporter=html --reporter=text _mocha --reporter spec"}, "engines": {"node": ">=12.0.0"}, "dependencies": {}, "standard": {"ignore": ["example/eventsource-polyfill.js"], "globals": ["URL"]}}