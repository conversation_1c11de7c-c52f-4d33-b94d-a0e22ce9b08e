{"version": 3, "sources": ["../../stompjs/lib/stomp.js", "browser-external:net", "../../es5-ext/global.js", "../../websocket/package.json", "../../websocket/lib/version.js", "../../websocket/lib/browser.js", "../../stompjs/lib/stomp-node.js", "../../stompjs/index.js"], "sourcesContent": ["// Generated by CoffeeScript 1.7.1\n\n/*\n   Stomp Over WebSocket http://www.jmesnil.net/stomp-websocket/doc/ | Apache License V2.0\n\n   Copyright (C) 2010-2013 [<PERSON>](http://jmesnil.net/)\n   Copyright (C) 2012 [FuseSource, Inc.](http://fusesource.com)\n */\n\n(function() {\n  var Byte, Client, Frame, Stomp,\n    __hasProp = {}.hasOwnProperty,\n    __slice = [].slice;\n\n  Byte = {\n    LF: '\\x0A',\n    NULL: '\\x00'\n  };\n\n  Frame = (function() {\n    var unmarshallSingle;\n\n    function Frame(command, headers, body) {\n      this.command = command;\n      this.headers = headers != null ? headers : {};\n      this.body = body != null ? body : '';\n    }\n\n    Frame.prototype.toString = function() {\n      var lines, name, skipContentLength, value, _ref;\n      lines = [this.command];\n      skipContentLength = this.headers['content-length'] === false ? true : false;\n      if (skipContentLength) {\n        delete this.headers['content-length'];\n      }\n      _ref = this.headers;\n      for (name in _ref) {\n        if (!__hasProp.call(_ref, name)) continue;\n        value = _ref[name];\n        lines.push(\"\" + name + \":\" + value);\n      }\n      if (this.body && !skipContentLength) {\n        lines.push(\"content-length:\" + (Frame.sizeOfUTF8(this.body)));\n      }\n      lines.push(Byte.LF + this.body);\n      return lines.join(Byte.LF);\n    };\n\n    Frame.sizeOfUTF8 = function(s) {\n      if (s) {\n        return encodeURI(s).match(/%..|./g).length;\n      } else {\n        return 0;\n      }\n    };\n\n    unmarshallSingle = function(data) {\n      var body, chr, command, divider, headerLines, headers, i, idx, len, line, start, trim, _i, _j, _len, _ref, _ref1;\n      divider = data.search(RegExp(\"\" + Byte.LF + Byte.LF));\n      headerLines = data.substring(0, divider).split(Byte.LF);\n      command = headerLines.shift();\n      headers = {};\n      trim = function(str) {\n        return str.replace(/^\\s+|\\s+$/g, '');\n      };\n      _ref = headerLines.reverse();\n      for (_i = 0, _len = _ref.length; _i < _len; _i++) {\n        line = _ref[_i];\n        idx = line.indexOf(':');\n        headers[trim(line.substring(0, idx))] = trim(line.substring(idx + 1));\n      }\n      body = '';\n      start = divider + 2;\n      if (headers['content-length']) {\n        len = parseInt(headers['content-length']);\n        body = ('' + data).substring(start, start + len);\n      } else {\n        chr = null;\n        for (i = _j = start, _ref1 = data.length; start <= _ref1 ? _j < _ref1 : _j > _ref1; i = start <= _ref1 ? ++_j : --_j) {\n          chr = data.charAt(i);\n          if (chr === Byte.NULL) {\n            break;\n          }\n          body += chr;\n        }\n      }\n      return new Frame(command, headers, body);\n    };\n\n    Frame.unmarshall = function(datas) {\n      var data;\n      return (function() {\n        var _i, _len, _ref, _results;\n        _ref = datas.split(RegExp(\"\" + Byte.NULL + Byte.LF + \"*\"));\n        _results = [];\n        for (_i = 0, _len = _ref.length; _i < _len; _i++) {\n          data = _ref[_i];\n          if ((data != null ? data.length : void 0) > 0) {\n            _results.push(unmarshallSingle(data));\n          }\n        }\n        return _results;\n      })();\n    };\n\n    Frame.marshall = function(command, headers, body) {\n      var frame;\n      frame = new Frame(command, headers, body);\n      return frame.toString() + Byte.NULL;\n    };\n\n    return Frame;\n\n  })();\n\n  Client = (function() {\n    var now;\n\n    function Client(ws) {\n      this.ws = ws;\n      this.ws.binaryType = \"arraybuffer\";\n      this.counter = 0;\n      this.connected = false;\n      this.heartbeat = {\n        outgoing: 10000,\n        incoming: 10000\n      };\n      this.maxWebSocketFrameSize = 16 * 1024;\n      this.subscriptions = {};\n    }\n\n    Client.prototype.debug = function(message) {\n      var _ref;\n      return typeof window !== \"undefined\" && window !== null ? (_ref = window.console) != null ? _ref.log(message) : void 0 : void 0;\n    };\n\n    now = function() {\n      if (Date.now) {\n        return Date.now();\n      } else {\n        return new Date().valueOf;\n      }\n    };\n\n    Client.prototype._transmit = function(command, headers, body) {\n      var out;\n      out = Frame.marshall(command, headers, body);\n      if (typeof this.debug === \"function\") {\n        this.debug(\">>> \" + out);\n      }\n      while (true) {\n        if (out.length > this.maxWebSocketFrameSize) {\n          this.ws.send(out.substring(0, this.maxWebSocketFrameSize));\n          out = out.substring(this.maxWebSocketFrameSize);\n          if (typeof this.debug === \"function\") {\n            this.debug(\"remaining = \" + out.length);\n          }\n        } else {\n          return this.ws.send(out);\n        }\n      }\n    };\n\n    Client.prototype._setupHeartbeat = function(headers) {\n      var serverIncoming, serverOutgoing, ttl, v, _ref, _ref1;\n      if ((_ref = headers.version) !== Stomp.VERSIONS.V1_1 && _ref !== Stomp.VERSIONS.V1_2) {\n        return;\n      }\n      _ref1 = (function() {\n        var _i, _len, _ref1, _results;\n        _ref1 = headers['heart-beat'].split(\",\");\n        _results = [];\n        for (_i = 0, _len = _ref1.length; _i < _len; _i++) {\n          v = _ref1[_i];\n          _results.push(parseInt(v));\n        }\n        return _results;\n      })(), serverOutgoing = _ref1[0], serverIncoming = _ref1[1];\n      if (!(this.heartbeat.outgoing === 0 || serverIncoming === 0)) {\n        ttl = Math.max(this.heartbeat.outgoing, serverIncoming);\n        if (typeof this.debug === \"function\") {\n          this.debug(\"send PING every \" + ttl + \"ms\");\n        }\n        this.pinger = Stomp.setInterval(ttl, (function(_this) {\n          return function() {\n            _this.ws.send(Byte.LF);\n            return typeof _this.debug === \"function\" ? _this.debug(\">>> PING\") : void 0;\n          };\n        })(this));\n      }\n      if (!(this.heartbeat.incoming === 0 || serverOutgoing === 0)) {\n        ttl = Math.max(this.heartbeat.incoming, serverOutgoing);\n        if (typeof this.debug === \"function\") {\n          this.debug(\"check PONG every \" + ttl + \"ms\");\n        }\n        return this.ponger = Stomp.setInterval(ttl, (function(_this) {\n          return function() {\n            var delta;\n            delta = now() - _this.serverActivity;\n            if (delta > ttl * 2) {\n              if (typeof _this.debug === \"function\") {\n                _this.debug(\"did not receive server activity for the last \" + delta + \"ms\");\n              }\n              return _this.ws.close();\n            }\n          };\n        })(this));\n      }\n    };\n\n    Client.prototype._parseConnect = function() {\n      var args, connectCallback, errorCallback, headers;\n      args = 1 <= arguments.length ? __slice.call(arguments, 0) : [];\n      headers = {};\n      switch (args.length) {\n        case 2:\n          headers = args[0], connectCallback = args[1];\n          break;\n        case 3:\n          if (args[1] instanceof Function) {\n            headers = args[0], connectCallback = args[1], errorCallback = args[2];\n          } else {\n            headers.login = args[0], headers.passcode = args[1], connectCallback = args[2];\n          }\n          break;\n        case 4:\n          headers.login = args[0], headers.passcode = args[1], connectCallback = args[2], errorCallback = args[3];\n          break;\n        default:\n          headers.login = args[0], headers.passcode = args[1], connectCallback = args[2], errorCallback = args[3], headers.host = args[4];\n      }\n      return [headers, connectCallback, errorCallback];\n    };\n\n    Client.prototype.connect = function() {\n      var args, errorCallback, headers, out;\n      args = 1 <= arguments.length ? __slice.call(arguments, 0) : [];\n      out = this._parseConnect.apply(this, args);\n      headers = out[0], this.connectCallback = out[1], errorCallback = out[2];\n      if (typeof this.debug === \"function\") {\n        this.debug(\"Opening Web Socket...\");\n      }\n      this.ws.onmessage = (function(_this) {\n        return function(evt) {\n          var arr, c, client, data, frame, messageID, onreceive, subscription, _i, _len, _ref, _results;\n          data = typeof ArrayBuffer !== 'undefined' && evt.data instanceof ArrayBuffer ? (arr = new Uint8Array(evt.data), typeof _this.debug === \"function\" ? _this.debug(\"--- got data length: \" + arr.length) : void 0, ((function() {\n            var _i, _len, _results;\n            _results = [];\n            for (_i = 0, _len = arr.length; _i < _len; _i++) {\n              c = arr[_i];\n              _results.push(String.fromCharCode(c));\n            }\n            return _results;\n          })()).join('')) : evt.data;\n          _this.serverActivity = now();\n          if (data === Byte.LF) {\n            if (typeof _this.debug === \"function\") {\n              _this.debug(\"<<< PONG\");\n            }\n            return;\n          }\n          if (typeof _this.debug === \"function\") {\n            _this.debug(\"<<< \" + data);\n          }\n          _ref = Frame.unmarshall(data);\n          _results = [];\n          for (_i = 0, _len = _ref.length; _i < _len; _i++) {\n            frame = _ref[_i];\n            switch (frame.command) {\n              case \"CONNECTED\":\n                if (typeof _this.debug === \"function\") {\n                  _this.debug(\"connected to server \" + frame.headers.server);\n                }\n                _this.connected = true;\n                _this._setupHeartbeat(frame.headers);\n                _results.push(typeof _this.connectCallback === \"function\" ? _this.connectCallback(frame) : void 0);\n                break;\n              case \"MESSAGE\":\n                subscription = frame.headers.subscription;\n                onreceive = _this.subscriptions[subscription] || _this.onreceive;\n                if (onreceive) {\n                  client = _this;\n                  messageID = frame.headers[\"message-id\"];\n                  frame.ack = function(headers) {\n                    if (headers == null) {\n                      headers = {};\n                    }\n                    return client.ack(messageID, subscription, headers);\n                  };\n                  frame.nack = function(headers) {\n                    if (headers == null) {\n                      headers = {};\n                    }\n                    return client.nack(messageID, subscription, headers);\n                  };\n                  _results.push(onreceive(frame));\n                } else {\n                  _results.push(typeof _this.debug === \"function\" ? _this.debug(\"Unhandled received MESSAGE: \" + frame) : void 0);\n                }\n                break;\n              case \"RECEIPT\":\n                _results.push(typeof _this.onreceipt === \"function\" ? _this.onreceipt(frame) : void 0);\n                break;\n              case \"ERROR\":\n                _results.push(typeof errorCallback === \"function\" ? errorCallback(frame) : void 0);\n                break;\n              default:\n                _results.push(typeof _this.debug === \"function\" ? _this.debug(\"Unhandled frame: \" + frame) : void 0);\n            }\n          }\n          return _results;\n        };\n      })(this);\n      this.ws.onclose = (function(_this) {\n        return function() {\n          var msg;\n          msg = \"Whoops! Lost connection to \" + _this.ws.url;\n          if (typeof _this.debug === \"function\") {\n            _this.debug(msg);\n          }\n          _this._cleanUp();\n          return typeof errorCallback === \"function\" ? errorCallback(msg) : void 0;\n        };\n      })(this);\n      return this.ws.onopen = (function(_this) {\n        return function() {\n          if (typeof _this.debug === \"function\") {\n            _this.debug('Web Socket Opened...');\n          }\n          headers[\"accept-version\"] = Stomp.VERSIONS.supportedVersions();\n          headers[\"heart-beat\"] = [_this.heartbeat.outgoing, _this.heartbeat.incoming].join(',');\n          return _this._transmit(\"CONNECT\", headers);\n        };\n      })(this);\n    };\n\n    Client.prototype.disconnect = function(disconnectCallback, headers) {\n      if (headers == null) {\n        headers = {};\n      }\n      this._transmit(\"DISCONNECT\", headers);\n      this.ws.onclose = null;\n      this.ws.close();\n      this._cleanUp();\n      return typeof disconnectCallback === \"function\" ? disconnectCallback() : void 0;\n    };\n\n    Client.prototype._cleanUp = function() {\n      this.connected = false;\n      if (this.pinger) {\n        Stomp.clearInterval(this.pinger);\n      }\n      if (this.ponger) {\n        return Stomp.clearInterval(this.ponger);\n      }\n    };\n\n    Client.prototype.send = function(destination, headers, body) {\n      if (headers == null) {\n        headers = {};\n      }\n      if (body == null) {\n        body = '';\n      }\n      headers.destination = destination;\n      return this._transmit(\"SEND\", headers, body);\n    };\n\n    Client.prototype.subscribe = function(destination, callback, headers) {\n      var client;\n      if (headers == null) {\n        headers = {};\n      }\n      if (!headers.id) {\n        headers.id = \"sub-\" + this.counter++;\n      }\n      headers.destination = destination;\n      this.subscriptions[headers.id] = callback;\n      this._transmit(\"SUBSCRIBE\", headers);\n      client = this;\n      return {\n        id: headers.id,\n        unsubscribe: function() {\n          return client.unsubscribe(headers.id);\n        }\n      };\n    };\n\n    Client.prototype.unsubscribe = function(id) {\n      delete this.subscriptions[id];\n      return this._transmit(\"UNSUBSCRIBE\", {\n        id: id\n      });\n    };\n\n    Client.prototype.begin = function(transaction) {\n      var client, txid;\n      txid = transaction || \"tx-\" + this.counter++;\n      this._transmit(\"BEGIN\", {\n        transaction: txid\n      });\n      client = this;\n      return {\n        id: txid,\n        commit: function() {\n          return client.commit(txid);\n        },\n        abort: function() {\n          return client.abort(txid);\n        }\n      };\n    };\n\n    Client.prototype.commit = function(transaction) {\n      return this._transmit(\"COMMIT\", {\n        transaction: transaction\n      });\n    };\n\n    Client.prototype.abort = function(transaction) {\n      return this._transmit(\"ABORT\", {\n        transaction: transaction\n      });\n    };\n\n    Client.prototype.ack = function(messageID, subscription, headers) {\n      if (headers == null) {\n        headers = {};\n      }\n      headers[\"message-id\"] = messageID;\n      headers.subscription = subscription;\n      return this._transmit(\"ACK\", headers);\n    };\n\n    Client.prototype.nack = function(messageID, subscription, headers) {\n      if (headers == null) {\n        headers = {};\n      }\n      headers[\"message-id\"] = messageID;\n      headers.subscription = subscription;\n      return this._transmit(\"NACK\", headers);\n    };\n\n    return Client;\n\n  })();\n\n  Stomp = {\n    VERSIONS: {\n      V1_0: '1.0',\n      V1_1: '1.1',\n      V1_2: '1.2',\n      supportedVersions: function() {\n        return '1.1,1.0';\n      }\n    },\n    client: function(url, protocols) {\n      var klass, ws;\n      if (protocols == null) {\n        protocols = ['v10.stomp', 'v11.stomp'];\n      }\n      klass = Stomp.WebSocketClass || WebSocket;\n      ws = new klass(url, protocols);\n      return new Client(ws);\n    },\n    over: function(ws) {\n      return new Client(ws);\n    },\n    Frame: Frame\n  };\n\n  if (typeof exports !== \"undefined\" && exports !== null) {\n    exports.Stomp = Stomp;\n  }\n\n  if (typeof window !== \"undefined\" && window !== null) {\n    Stomp.setInterval = function(interval, f) {\n      return window.setInterval(f, interval);\n    };\n    Stomp.clearInterval = function(id) {\n      return window.clearInterval(id);\n    };\n    window.Stomp = Stomp;\n  } else if (!exports) {\n    self.Stomp = Stomp;\n  }\n\n}).call(this);\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"net\" has been externalized for browser compatibility. Cannot access \"net.${key}\" in client code. See http://vitejs.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "var naiveFallback = function () {\n\tif (typeof self === \"object\" && self) return self;\n\tif (typeof window === \"object\" && window) return window;\n\tthrow new Error(\"Unable to resolve global `this`\");\n};\n\nmodule.exports = (function () {\n\tif (this) return this;\n\n\t// Unexpected strict mode (may happen if e.g. bundled into ESM module)\n\n\t// Fallback to standard globalThis if available\n\tif (typeof globalThis === \"object\" && globalThis) return globalThis;\n\n\t// Thanks @mathiasbynens -> https://mathiasbynens.be/notes/globalthis\n\t// In all ES5+ engines global object inherits from Object.prototype\n\t// (if you approached one that doesn't please report)\n\ttry {\n\t\tObject.defineProperty(Object.prototype, \"__global__\", {\n\t\t\tget: function () { return this; },\n\t\t\tconfigurable: true\n\t\t});\n\t} catch (error) {\n\t\t// Unfortunate case of updates to Object.prototype being restricted\n\t\t// via preventExtensions, seal or freeze\n\t\treturn naiveFallback();\n\t}\n\ttry {\n\t\t// Safari case (window.__global__ works, but __global__ does not)\n\t\tif (!__global__) return naiveFallback();\n\t\treturn __global__;\n\t} finally {\n\t\tdelete Object.prototype.__global__;\n\t}\n})();\n", "{\n  \"name\": \"websocket\",\n  \"description\": \"Websocket Client & Server Library implementing the WebSocket protocol as specified in RFC 6455.\",\n  \"keywords\": [\n    \"websocket\",\n    \"websockets\",\n    \"socket\",\n    \"networking\",\n    \"comet\",\n    \"push\",\n    \"RFC-6455\",\n    \"realtime\",\n    \"server\",\n    \"client\"\n  ],\n  \"author\": \"<PERSON> <<EMAIL>> (https://github.com/theturtle32)\",\n  \"contributors\": [\n    \"<PERSON><PERSON><PERSON> <<EMAIL>> (http://dev.sipdoc.net)\"\n  ],\n  \"version\": \"1.0.35\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/theturtle32/WebSocket-Node.git\"\n  },\n  \"homepage\": \"https://github.com/theturtle32/WebSocket-Node\",\n  \"engines\": {\n    \"node\": \">=4.0.0\"\n  },\n  \"dependencies\": {\n    \"bufferutil\": \"^4.0.1\",\n    \"debug\": \"^2.2.0\",\n    \"es5-ext\": \"^0.10.63\",\n    \"typedarray-to-buffer\": \"^3.1.5\",\n    \"utf-8-validate\": \"^5.0.2\",\n    \"yaeti\": \"^0.0.6\"\n  },\n  \"devDependencies\": {\n    \"buffer-equal\": \"^1.0.0\",\n    \"gulp\": \"^4.0.2\",\n    \"gulp-jshint\": \"^2.0.4\",\n    \"jshint-stylish\": \"^2.2.1\",\n    \"jshint\": \"^2.0.0\",\n    \"tape\": \"^4.9.1\"\n  },\n  \"config\": {\n    \"verbose\": false\n  },\n  \"scripts\": {\n    \"test\": \"tape test/unit/*.js\",\n    \"gulp\": \"gulp\"\n  },\n  \"main\": \"index\",\n  \"directories\": {\n    \"lib\": \"./lib\"\n  },\n  \"browser\": \"lib/browser.js\",\n  \"license\": \"Apache-2.0\"\n}\n", "module.exports = require('../package.json').version;\n", "var _globalThis;\nif (typeof globalThis === 'object') {\n\t_globalThis = globalThis;\n} else {\n\ttry {\n\t\t_globalThis = require('es5-ext/global');\n\t} catch (error) {\n\t} finally {\n\t\tif (!_globalThis && typeof window !== 'undefined') { _globalThis = window; }\n\t\tif (!_globalThis) { throw new Error('Could not determine global this'); }\n\t}\n}\n\nvar NativeWebSocket = _globalThis.WebSocket || _globalThis.MozWebSocket;\nvar websocket_version = require('./version');\n\n\n/**\n * Expose a W3C WebSocket class with just one or two arguments.\n */\nfunction W3CWebSocket(uri, protocols) {\n\tvar native_instance;\n\n\tif (protocols) {\n\t\tnative_instance = new NativeWebSocket(uri, protocols);\n\t}\n\telse {\n\t\tnative_instance = new NativeWebSocket(uri);\n\t}\n\n\t/**\n\t * 'native_instance' is an instance of nativeWebSocket (the browser's WebSocket\n\t * class). Since it is an Object it will be returned as it is when creating an\n\t * instance of W3CWebSocket via 'new W3CWebSocket()'.\n\t *\n\t * ECMAScript 5: http://bclary.com/2004/11/07/#a-13.2.2\n\t */\n\treturn native_instance;\n}\nif (NativeWebSocket) {\n\t['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'].forEach(function(prop) {\n\t\tObject.defineProperty(W3CWebSocket, prop, {\n\t\t\tget: function() { return NativeWebSocket[prop]; }\n\t\t});\n\t});\n}\n\n/**\n * Module exports.\n */\nmodule.exports = {\n    'w3cwebsocket' : NativeWebSocket ? W3CWebSocket : null,\n    'version'      : websocket_version\n};\n", "// Generated by CoffeeScript 1.7.1\n\n/*\n   Stomp Over WebSocket http://www.jmesnil.net/stomp-websocket/doc/ | Apache License V2.0\n\n   Copyright (C) 2013 [<PERSON>](http://jmesnil.net/)\n */\n\n(function() {\n  var Stomp, net, overTCP, overWS, wrapTCP, wrapWS;\n\n  Stomp = require('./stomp');\n\n  net = require('net');\n\n  Stomp.Stomp.setInterval = function(interval, f) {\n    return setInterval(f, interval);\n  };\n\n  Stomp.Stomp.clearInterval = function(id) {\n    return clearInterval(id);\n  };\n\n  wrapTCP = function(port, host) {\n    var socket, ws;\n    socket = null;\n    ws = {\n      url: 'tcp:// ' + host + ':' + port,\n      send: function(d) {\n        return socket.write(d);\n      },\n      close: function() {\n        return socket.end();\n      }\n    };\n    socket = net.connect(port, host, function(e) {\n      return ws.onopen();\n    });\n    socket.on('error', function(e) {\n      return typeof ws.onclose === \"function\" ? ws.onclose(e) : void 0;\n    });\n    socket.on('close', function(e) {\n      return typeof ws.onclose === \"function\" ? ws.onclose(e) : void 0;\n    });\n    socket.on('data', function(data) {\n      var event;\n      event = {\n        'data': data.toString()\n      };\n      return ws.onmessage(event);\n    });\n    return ws;\n  };\n\n  wrapWS = function(url) {\n    var WebSocketClient, connection, socket, ws;\n    WebSocketClient = require('websocket').client;\n    connection = null;\n    ws = {\n      url: url,\n      send: function(d) {\n        return connection.sendUTF(d);\n      },\n      close: function() {\n        return connection.close();\n      }\n    };\n    socket = new WebSocketClient();\n    socket.on('connect', function(conn) {\n      connection = conn;\n      ws.onopen();\n      connection.on('error', function(error) {\n        return typeof ws.onclose === \"function\" ? ws.onclose(error) : void 0;\n      });\n      connection.on('close', function() {\n        return typeof ws.onclose === \"function\" ? ws.onclose() : void 0;\n      });\n      return connection.on('message', function(message) {\n        var event;\n        if (message.type === 'utf8') {\n          event = {\n            'data': message.utf8Data\n          };\n          return ws.onmessage(event);\n        }\n      });\n    });\n    socket.connect(url);\n    return ws;\n  };\n\n  overTCP = function(host, port) {\n    var socket;\n    socket = wrapTCP(port, host);\n    return Stomp.Stomp.over(socket);\n  };\n\n  overWS = function(url) {\n    var socket;\n    socket = wrapWS(url);\n    return Stomp.Stomp.over(socket);\n  };\n\n  exports.overTCP = overTCP;\n\n  exports.overWS = overWS;\n\n}).call(this);\n", "// Copyright (C) 2013 [<PERSON>](http://jmesnil.net/)\n//\n//   Stomp Over WebSocket http://www.jmesnil.net/stomp-websocket/doc/ | Apache License V2.0\n//\n// The library can be used in node.js app to connect to STOMP brokers over TCP \n// or Web sockets.\n\n// Root of the `stompjs module`\n\nvar Stomp = require('./lib/stomp.js');\nvar StompNode = require('./lib/stomp-node.js');\n\nmodule.exports = Stomp.Stomp;\nmodule.exports.overTCP = StompNode.overTCP;\nmodule.exports.overWS = StompNode.overWS;"], "mappings": ";;;;;AAAA;AAAA;AASA,KAAC,WAAW;AACV,UAAI,MAAM,QAAQ,OAAO,OACvB,YAAY,CAAC,EAAE,gBACf,UAAU,CAAC,EAAE;AAEf,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AAEA,cAAS,WAAW;AAClB,YAAI;AAEJ,iBAASA,OAAM,SAAS,SAAS,MAAM;AACrC,eAAK,UAAU;AACf,eAAK,UAAU,WAAW,OAAO,UAAU,CAAC;AAC5C,eAAK,OAAO,QAAQ,OAAO,OAAO;AAAA,QACpC;AAEA,QAAAA,OAAM,UAAU,WAAW,WAAW;AACpC,cAAI,OAAO,MAAM,mBAAmB,OAAO;AAC3C,kBAAQ,CAAC,KAAK,OAAO;AACrB,8BAAoB,KAAK,QAAQ,gBAAgB,MAAM,QAAQ,OAAO;AACtE,cAAI,mBAAmB;AACrB,mBAAO,KAAK,QAAQ,gBAAgB;AAAA,UACtC;AACA,iBAAO,KAAK;AACZ,eAAK,QAAQ,MAAM;AACjB,gBAAI,CAAC,UAAU,KAAK,MAAM,IAAI;AAAG;AACjC,oBAAQ,KAAK,IAAI;AACjB,kBAAM,KAAK,KAAK,OAAO,MAAM,KAAK;AAAA,UACpC;AACA,cAAI,KAAK,QAAQ,CAAC,mBAAmB;AACnC,kBAAM,KAAK,oBAAqBA,OAAM,WAAW,KAAK,IAAI,CAAE;AAAA,UAC9D;AACA,gBAAM,KAAK,KAAK,KAAK,KAAK,IAAI;AAC9B,iBAAO,MAAM,KAAK,KAAK,EAAE;AAAA,QAC3B;AAEA,QAAAA,OAAM,aAAa,SAAS,GAAG;AAC7B,cAAI,GAAG;AACL,mBAAO,UAAU,CAAC,EAAE,MAAM,QAAQ,EAAE;AAAA,UACtC,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,2BAAmB,SAAS,MAAM;AAChC,cAAI,MAAM,KAAK,SAAS,SAAS,aAAa,SAAS,GAAG,KAAK,KAAK,MAAM,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM;AAC3G,oBAAU,KAAK,OAAO,OAAO,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC;AACpD,wBAAc,KAAK,UAAU,GAAG,OAAO,EAAE,MAAM,KAAK,EAAE;AACtD,oBAAU,YAAY,MAAM;AAC5B,oBAAU,CAAC;AACX,iBAAO,SAAS,KAAK;AACnB,mBAAO,IAAI,QAAQ,cAAc,EAAE;AAAA,UACrC;AACA,iBAAO,YAAY,QAAQ;AAC3B,eAAK,KAAK,GAAG,OAAO,KAAK,QAAQ,KAAK,MAAM,MAAM;AAChD,mBAAO,KAAK,EAAE;AACd,kBAAM,KAAK,QAAQ,GAAG;AACtB,oBAAQ,KAAK,KAAK,UAAU,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,UAAU,MAAM,CAAC,CAAC;AAAA,UACtE;AACA,iBAAO;AACP,kBAAQ,UAAU;AAClB,cAAI,QAAQ,gBAAgB,GAAG;AAC7B,kBAAM,SAAS,QAAQ,gBAAgB,CAAC;AACxC,oBAAQ,KAAK,MAAM,UAAU,OAAO,QAAQ,GAAG;AAAA,UACjD,OAAO;AACL,kBAAM;AACN,iBAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,QAAQ,SAAS,QAAQ,KAAK,QAAQ,KAAK,OAAO,IAAI,SAAS,QAAQ,EAAE,KAAK,EAAE,IAAI;AACpH,oBAAM,KAAK,OAAO,CAAC;AACnB,kBAAI,QAAQ,KAAK,MAAM;AACrB;AAAA,cACF;AACA,sBAAQ;AAAA,YACV;AAAA,UACF;AACA,iBAAO,IAAIA,OAAM,SAAS,SAAS,IAAI;AAAA,QACzC;AAEA,QAAAA,OAAM,aAAa,SAAS,OAAO;AACjC,cAAI;AACJ,iBAAQ,WAAW;AACjB,gBAAI,IAAI,MAAM,MAAM;AACpB,mBAAO,MAAM,MAAM,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,GAAG,CAAC;AACzD,uBAAW,CAAC;AACZ,iBAAK,KAAK,GAAG,OAAO,KAAK,QAAQ,KAAK,MAAM,MAAM;AAChD,qBAAO,KAAK,EAAE;AACd,mBAAK,QAAQ,OAAO,KAAK,SAAS,UAAU,GAAG;AAC7C,yBAAS,KAAK,iBAAiB,IAAI,CAAC;AAAA,cACtC;AAAA,YACF;AACA,mBAAO;AAAA,UACT,EAAG;AAAA,QACL;AAEA,QAAAA,OAAM,WAAW,SAAS,SAAS,SAAS,MAAM;AAChD,cAAI;AACJ,kBAAQ,IAAIA,OAAM,SAAS,SAAS,IAAI;AACxC,iBAAO,MAAM,SAAS,IAAI,KAAK;AAAA,QACjC;AAEA,eAAOA;AAAA,MAET,EAAG;AAEH,eAAU,WAAW;AACnB,YAAI;AAEJ,iBAASC,QAAO,IAAI;AAClB,eAAK,KAAK;AACV,eAAK,GAAG,aAAa;AACrB,eAAK,UAAU;AACf,eAAK,YAAY;AACjB,eAAK,YAAY;AAAA,YACf,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AACA,eAAK,wBAAwB,KAAK;AAClC,eAAK,gBAAgB,CAAC;AAAA,QACxB;AAEA,QAAAA,QAAO,UAAU,QAAQ,SAAS,SAAS;AACzC,cAAI;AACJ,iBAAO,OAAO,WAAW,eAAe,WAAW,QAAQ,OAAO,OAAO,YAAY,OAAO,KAAK,IAAI,OAAO,IAAI,SAAS;AAAA,QAC3H;AAEA,cAAM,WAAW;AACf,cAAI,KAAK,KAAK;AACZ,mBAAO,KAAK,IAAI;AAAA,UAClB,OAAO;AACL,oBAAO,oBAAI,KAAK,GAAE;AAAA,UACpB;AAAA,QACF;AAEA,QAAAA,QAAO,UAAU,YAAY,SAAS,SAAS,SAAS,MAAM;AAC5D,cAAI;AACJ,gBAAM,MAAM,SAAS,SAAS,SAAS,IAAI;AAC3C,cAAI,OAAO,KAAK,UAAU,YAAY;AACpC,iBAAK,MAAM,SAAS,GAAG;AAAA,UACzB;AACA,iBAAO,MAAM;AACX,gBAAI,IAAI,SAAS,KAAK,uBAAuB;AAC3C,mBAAK,GAAG,KAAK,IAAI,UAAU,GAAG,KAAK,qBAAqB,CAAC;AACzD,oBAAM,IAAI,UAAU,KAAK,qBAAqB;AAC9C,kBAAI,OAAO,KAAK,UAAU,YAAY;AACpC,qBAAK,MAAM,iBAAiB,IAAI,MAAM;AAAA,cACxC;AAAA,YACF,OAAO;AACL,qBAAO,KAAK,GAAG,KAAK,GAAG;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAEA,QAAAA,QAAO,UAAU,kBAAkB,SAAS,SAAS;AACnD,cAAI,gBAAgB,gBAAgB,KAAK,GAAG,MAAM;AAClD,eAAK,OAAO,QAAQ,aAAa,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,MAAM;AACpF;AAAA,UACF;AACA,kBAAS,WAAW;AAClB,gBAAI,IAAI,MAAMC,QAAO;AACrB,YAAAA,SAAQ,QAAQ,YAAY,EAAE,MAAM,GAAG;AACvC,uBAAW,CAAC;AACZ,iBAAK,KAAK,GAAG,OAAOA,OAAM,QAAQ,KAAK,MAAM,MAAM;AACjD,kBAAIA,OAAM,EAAE;AACZ,uBAAS,KAAK,SAAS,CAAC,CAAC;AAAA,YAC3B;AACA,mBAAO;AAAA,UACT,EAAG,GAAG,iBAAiB,MAAM,CAAC,GAAG,iBAAiB,MAAM,CAAC;AACzD,cAAI,EAAE,KAAK,UAAU,aAAa,KAAK,mBAAmB,IAAI;AAC5D,kBAAM,KAAK,IAAI,KAAK,UAAU,UAAU,cAAc;AACtD,gBAAI,OAAO,KAAK,UAAU,YAAY;AACpC,mBAAK,MAAM,qBAAqB,MAAM,IAAI;AAAA,YAC5C;AACA,iBAAK,SAAS,MAAM,YAAY,KAAM,SAAS,OAAO;AACpD,qBAAO,WAAW;AAChB,sBAAM,GAAG,KAAK,KAAK,EAAE;AACrB,uBAAO,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,cACvE;AAAA,YACF,EAAG,IAAI,CAAC;AAAA,UACV;AACA,cAAI,EAAE,KAAK,UAAU,aAAa,KAAK,mBAAmB,IAAI;AAC5D,kBAAM,KAAK,IAAI,KAAK,UAAU,UAAU,cAAc;AACtD,gBAAI,OAAO,KAAK,UAAU,YAAY;AACpC,mBAAK,MAAM,sBAAsB,MAAM,IAAI;AAAA,YAC7C;AACA,mBAAO,KAAK,SAAS,MAAM,YAAY,KAAM,SAAS,OAAO;AAC3D,qBAAO,WAAW;AAChB,oBAAI;AACJ,wBAAQ,IAAI,IAAI,MAAM;AACtB,oBAAI,QAAQ,MAAM,GAAG;AACnB,sBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,0BAAM,MAAM,kDAAkD,QAAQ,IAAI;AAAA,kBAC5E;AACA,yBAAO,MAAM,GAAG,MAAM;AAAA,gBACxB;AAAA,cACF;AAAA,YACF,EAAG,IAAI,CAAC;AAAA,UACV;AAAA,QACF;AAEA,QAAAD,QAAO,UAAU,gBAAgB,WAAW;AAC1C,cAAI,MAAM,iBAAiB,eAAe;AAC1C,iBAAO,KAAK,UAAU,SAAS,QAAQ,KAAK,WAAW,CAAC,IAAI,CAAC;AAC7D,oBAAU,CAAC;AACX,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AACH,wBAAU,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC;AAC3C;AAAA,YACF,KAAK;AACH,kBAAI,KAAK,CAAC,aAAa,UAAU;AAC/B,0BAAU,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC,GAAG,gBAAgB,KAAK,CAAC;AAAA,cACtE,OAAO;AACL,wBAAQ,QAAQ,KAAK,CAAC,GAAG,QAAQ,WAAW,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC;AAAA,cAC/E;AACA;AAAA,YACF,KAAK;AACH,sBAAQ,QAAQ,KAAK,CAAC,GAAG,QAAQ,WAAW,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC,GAAG,gBAAgB,KAAK,CAAC;AACtG;AAAA,YACF;AACE,sBAAQ,QAAQ,KAAK,CAAC,GAAG,QAAQ,WAAW,KAAK,CAAC,GAAG,kBAAkB,KAAK,CAAC,GAAG,gBAAgB,KAAK,CAAC,GAAG,QAAQ,OAAO,KAAK,CAAC;AAAA,UAClI;AACA,iBAAO,CAAC,SAAS,iBAAiB,aAAa;AAAA,QACjD;AAEA,QAAAA,QAAO,UAAU,UAAU,WAAW;AACpC,cAAI,MAAM,eAAe,SAAS;AAClC,iBAAO,KAAK,UAAU,SAAS,QAAQ,KAAK,WAAW,CAAC,IAAI,CAAC;AAC7D,gBAAM,KAAK,cAAc,MAAM,MAAM,IAAI;AACzC,oBAAU,IAAI,CAAC,GAAG,KAAK,kBAAkB,IAAI,CAAC,GAAG,gBAAgB,IAAI,CAAC;AACtE,cAAI,OAAO,KAAK,UAAU,YAAY;AACpC,iBAAK,MAAM,uBAAuB;AAAA,UACpC;AACA,eAAK,GAAG,YAAa,SAAS,OAAO;AACnC,mBAAO,SAAS,KAAK;AACnB,kBAAI,KAAK,GAAG,QAAQ,MAAM,OAAO,WAAW,WAAW,cAAc,IAAI,MAAM,MAAM;AACrF,qBAAO,OAAO,gBAAgB,eAAe,IAAI,gBAAgB,eAAe,MAAM,IAAI,WAAW,IAAI,IAAI,GAAG,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,0BAA0B,IAAI,MAAM,IAAI,QAAU,WAAW;AAC3N,oBAAIE,KAAIC,OAAMC;AACd,gBAAAA,YAAW,CAAC;AACZ,qBAAKF,MAAK,GAAGC,QAAO,IAAI,QAAQD,MAAKC,OAAMD,OAAM;AAC/C,sBAAI,IAAIA,GAAE;AACV,kBAAAE,UAAS,KAAK,OAAO,aAAa,CAAC,CAAC;AAAA,gBACtC;AACA,uBAAOA;AAAA,cACT,EAAG,EAAG,KAAK,EAAE,KAAK,IAAI;AACtB,oBAAM,iBAAiB,IAAI;AAC3B,kBAAI,SAAS,KAAK,IAAI;AACpB,oBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,wBAAM,MAAM,UAAU;AAAA,gBACxB;AACA;AAAA,cACF;AACA,kBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,sBAAM,MAAM,SAAS,IAAI;AAAA,cAC3B;AACA,qBAAO,MAAM,WAAW,IAAI;AAC5B,yBAAW,CAAC;AACZ,mBAAK,KAAK,GAAG,OAAO,KAAK,QAAQ,KAAK,MAAM,MAAM;AAChD,wBAAQ,KAAK,EAAE;AACf,wBAAQ,MAAM,SAAS;AAAA,kBACrB,KAAK;AACH,wBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,4BAAM,MAAM,yBAAyB,MAAM,QAAQ,MAAM;AAAA,oBAC3D;AACA,0BAAM,YAAY;AAClB,0BAAM,gBAAgB,MAAM,OAAO;AACnC,6BAAS,KAAK,OAAO,MAAM,oBAAoB,aAAa,MAAM,gBAAgB,KAAK,IAAI,MAAM;AACjG;AAAA,kBACF,KAAK;AACH,mCAAe,MAAM,QAAQ;AAC7B,gCAAY,MAAM,cAAc,YAAY,KAAK,MAAM;AACvD,wBAAI,WAAW;AACb,+BAAS;AACT,kCAAY,MAAM,QAAQ,YAAY;AACtC,4BAAM,MAAM,SAASC,UAAS;AAC5B,4BAAIA,YAAW,MAAM;AACnB,0BAAAA,WAAU,CAAC;AAAA,wBACb;AACA,+BAAO,OAAO,IAAI,WAAW,cAAcA,QAAO;AAAA,sBACpD;AACA,4BAAM,OAAO,SAASA,UAAS;AAC7B,4BAAIA,YAAW,MAAM;AACnB,0BAAAA,WAAU,CAAC;AAAA,wBACb;AACA,+BAAO,OAAO,KAAK,WAAW,cAAcA,QAAO;AAAA,sBACrD;AACA,+BAAS,KAAK,UAAU,KAAK,CAAC;AAAA,oBAChC,OAAO;AACL,+BAAS,KAAK,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,iCAAiC,KAAK,IAAI,MAAM;AAAA,oBAChH;AACA;AAAA,kBACF,KAAK;AACH,6BAAS,KAAK,OAAO,MAAM,cAAc,aAAa,MAAM,UAAU,KAAK,IAAI,MAAM;AACrF;AAAA,kBACF,KAAK;AACH,6BAAS,KAAK,OAAO,kBAAkB,aAAa,cAAc,KAAK,IAAI,MAAM;AACjF;AAAA,kBACF;AACE,6BAAS,KAAK,OAAO,MAAM,UAAU,aAAa,MAAM,MAAM,sBAAsB,KAAK,IAAI,MAAM;AAAA,gBACvG;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAAA,UACF,EAAG,IAAI;AACP,eAAK,GAAG,UAAW,SAAS,OAAO;AACjC,mBAAO,WAAW;AAChB,kBAAI;AACJ,oBAAM,gCAAgC,MAAM,GAAG;AAC/C,kBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,sBAAM,MAAM,GAAG;AAAA,cACjB;AACA,oBAAM,SAAS;AACf,qBAAO,OAAO,kBAAkB,aAAa,cAAc,GAAG,IAAI;AAAA,YACpE;AAAA,UACF,EAAG,IAAI;AACP,iBAAO,KAAK,GAAG,SAAU,SAAS,OAAO;AACvC,mBAAO,WAAW;AAChB,kBAAI,OAAO,MAAM,UAAU,YAAY;AACrC,sBAAM,MAAM,sBAAsB;AAAA,cACpC;AACA,sBAAQ,gBAAgB,IAAI,MAAM,SAAS,kBAAkB;AAC7D,sBAAQ,YAAY,IAAI,CAAC,MAAM,UAAU,UAAU,MAAM,UAAU,QAAQ,EAAE,KAAK,GAAG;AACrF,qBAAO,MAAM,UAAU,WAAW,OAAO;AAAA,YAC3C;AAAA,UACF,EAAG,IAAI;AAAA,QACT;AAEA,QAAAL,QAAO,UAAU,aAAa,SAAS,oBAAoB,SAAS;AAClE,cAAI,WAAW,MAAM;AACnB,sBAAU,CAAC;AAAA,UACb;AACA,eAAK,UAAU,cAAc,OAAO;AACpC,eAAK,GAAG,UAAU;AAClB,eAAK,GAAG,MAAM;AACd,eAAK,SAAS;AACd,iBAAO,OAAO,uBAAuB,aAAa,mBAAmB,IAAI;AAAA,QAC3E;AAEA,QAAAA,QAAO,UAAU,WAAW,WAAW;AACrC,eAAK,YAAY;AACjB,cAAI,KAAK,QAAQ;AACf,kBAAM,cAAc,KAAK,MAAM;AAAA,UACjC;AACA,cAAI,KAAK,QAAQ;AACf,mBAAO,MAAM,cAAc,KAAK,MAAM;AAAA,UACxC;AAAA,QACF;AAEA,QAAAA,QAAO,UAAU,OAAO,SAAS,aAAa,SAAS,MAAM;AAC3D,cAAI,WAAW,MAAM;AACnB,sBAAU,CAAC;AAAA,UACb;AACA,cAAI,QAAQ,MAAM;AAChB,mBAAO;AAAA,UACT;AACA,kBAAQ,cAAc;AACtB,iBAAO,KAAK,UAAU,QAAQ,SAAS,IAAI;AAAA,QAC7C;AAEA,QAAAA,QAAO,UAAU,YAAY,SAAS,aAAa,UAAU,SAAS;AACpE,cAAI;AACJ,cAAI,WAAW,MAAM;AACnB,sBAAU,CAAC;AAAA,UACb;AACA,cAAI,CAAC,QAAQ,IAAI;AACf,oBAAQ,KAAK,SAAS,KAAK;AAAA,UAC7B;AACA,kBAAQ,cAAc;AACtB,eAAK,cAAc,QAAQ,EAAE,IAAI;AACjC,eAAK,UAAU,aAAa,OAAO;AACnC,mBAAS;AACT,iBAAO;AAAA,YACL,IAAI,QAAQ;AAAA,YACZ,aAAa,WAAW;AACtB,qBAAO,OAAO,YAAY,QAAQ,EAAE;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAEA,QAAAA,QAAO,UAAU,cAAc,SAAS,IAAI;AAC1C,iBAAO,KAAK,cAAc,EAAE;AAC5B,iBAAO,KAAK,UAAU,eAAe;AAAA,YACnC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,QAAAA,QAAO,UAAU,QAAQ,SAAS,aAAa;AAC7C,cAAI,QAAQ;AACZ,iBAAO,eAAe,QAAQ,KAAK;AACnC,eAAK,UAAU,SAAS;AAAA,YACtB,aAAa;AAAA,UACf,CAAC;AACD,mBAAS;AACT,iBAAO;AAAA,YACL,IAAI;AAAA,YACJ,QAAQ,WAAW;AACjB,qBAAO,OAAO,OAAO,IAAI;AAAA,YAC3B;AAAA,YACA,OAAO,WAAW;AAChB,qBAAO,OAAO,MAAM,IAAI;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAEA,QAAAA,QAAO,UAAU,SAAS,SAAS,aAAa;AAC9C,iBAAO,KAAK,UAAU,UAAU;AAAA,YAC9B;AAAA,UACF,CAAC;AAAA,QACH;AAEA,QAAAA,QAAO,UAAU,QAAQ,SAAS,aAAa;AAC7C,iBAAO,KAAK,UAAU,SAAS;AAAA,YAC7B;AAAA,UACF,CAAC;AAAA,QACH;AAEA,QAAAA,QAAO,UAAU,MAAM,SAAS,WAAW,cAAc,SAAS;AAChE,cAAI,WAAW,MAAM;AACnB,sBAAU,CAAC;AAAA,UACb;AACA,kBAAQ,YAAY,IAAI;AACxB,kBAAQ,eAAe;AACvB,iBAAO,KAAK,UAAU,OAAO,OAAO;AAAA,QACtC;AAEA,QAAAA,QAAO,UAAU,OAAO,SAAS,WAAW,cAAc,SAAS;AACjE,cAAI,WAAW,MAAM;AACnB,sBAAU,CAAC;AAAA,UACb;AACA,kBAAQ,YAAY,IAAI;AACxB,kBAAQ,eAAe;AACvB,iBAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,QACvC;AAEA,eAAOA;AAAA,MAET,EAAG;AAEH,cAAQ;AAAA,QACN,UAAU;AAAA,UACR,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,mBAAmB,WAAW;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,QAAQ,SAAS,KAAK,WAAW;AAC/B,cAAI,OAAO;AACX,cAAI,aAAa,MAAM;AACrB,wBAAY,CAAC,aAAa,WAAW;AAAA,UACvC;AACA,kBAAQ,MAAM,kBAAkB;AAChC,eAAK,IAAI,MAAM,KAAK,SAAS;AAC7B,iBAAO,IAAI,OAAO,EAAE;AAAA,QACtB;AAAA,QACA,MAAM,SAAS,IAAI;AACjB,iBAAO,IAAI,OAAO,EAAE;AAAA,QACtB;AAAA,QACA;AAAA,MACF;AAEA,UAAI,OAAO,YAAY,eAAe,YAAY,MAAM;AACtD,gBAAQ,QAAQ;AAAA,MAClB;AAEA,UAAI,OAAO,WAAW,eAAe,WAAW,MAAM;AACpD,cAAM,cAAc,SAAS,UAAU,GAAG;AACxC,iBAAO,OAAO,YAAY,GAAG,QAAQ;AAAA,QACvC;AACA,cAAM,gBAAgB,SAAS,IAAI;AACjC,iBAAO,OAAO,cAAc,EAAE;AAAA,QAChC;AACA,eAAO,QAAQ;AAAA,MACjB,WAAW,CAAC,SAAS;AACnB,aAAK,QAAQ;AAAA,MACf;AAAA,IAEF,GAAG,KAAK,OAAI;AAAA;AAAA;;;ACveZ;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oFAAoF,GAAG,oIAAoI;AAAA,QAC1O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,QAAI,gBAAgB,WAAY;AAC/B,UAAI,OAAO,SAAS,YAAY;AAAM,eAAO;AAC7C,UAAI,OAAO,WAAW,YAAY;AAAQ,eAAO;AACjD,YAAM,IAAI,MAAM,iCAAiC;AAAA,IAClD;AAEA,WAAO,UAAW,WAAY;AAC7B,UAAI;AAAM,eAAO;AAKjB,UAAI,OAAO,eAAe,YAAY;AAAY,eAAO;AAKzD,UAAI;AACH,eAAO,eAAe,OAAO,WAAW,cAAc;AAAA,UACrD,KAAK,WAAY;AAAE,mBAAO;AAAA,UAAM;AAAA,UAChC,cAAc;AAAA,QACf,CAAC;AAAA,MACF,SAAS,OAAO;AAGf,eAAO,cAAc;AAAA,MACtB;AACA,UAAI;AAEH,YAAI,CAAC;AAAY,iBAAO,cAAc;AACtC,eAAO;AAAA,MACR,UAAE;AACD,eAAO,OAAO,UAAU;AAAA,MACzB;AAAA,IACD,EAAG;AAAA;AAAA;;;AClCH;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,aAAe;AAAA,MACf,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,MACV,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,SAAW;AAAA,MACX,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,UAAY;AAAA,MACZ,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,cAAgB;AAAA,QACd,YAAc;AAAA,QACd,OAAS;AAAA,QACT,WAAW;AAAA,QACX,wBAAwB;AAAA,QACxB,kBAAkB;AAAA,QAClB,OAAS;AAAA,MACX;AAAA,MACA,iBAAmB;AAAA,QACjB,gBAAgB;AAAA,QAChB,MAAQ;AAAA,QACR,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,QAAU;AAAA,QACV,MAAQ;AAAA,MACV;AAAA,MACA,QAAU;AAAA,QACR,SAAW;AAAA,MACb;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,QACR,MAAQ;AAAA,MACV;AAAA,MACA,MAAQ;AAAA,MACR,aAAe;AAAA,QACb,KAAO;AAAA,MACT;AAAA,MACA,SAAW;AAAA,MACX,SAAW;AAAA,IACb;AAAA;AAAA;;;ACzDA;AAAA;AAAA,WAAO,UAAU,kBAA2B;AAAA;AAAA;;;ACA5C;AAAA;AAAA,QAAI;AACJ,QAAI,OAAO,eAAe,UAAU;AACnC,oBAAc;AAAA,IACf,OAAO;AACN,UAAI;AACH,sBAAc;AAAA,MACf,SAAS,OAAO;AAAA,MAChB,UAAE;AACD,YAAI,CAAC,eAAe,OAAO,WAAW,aAAa;AAAE,wBAAc;AAAA,QAAQ;AAC3E,YAAI,CAAC,aAAa;AAAE,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QAAG;AAAA,MACzE;AAAA,IACD;AAEA,QAAI,kBAAkB,YAAY,aAAa,YAAY;AAC3D,QAAI,oBAAoB;AAMxB,aAAS,aAAa,KAAK,WAAW;AACrC,UAAI;AAEJ,UAAI,WAAW;AACd,0BAAkB,IAAI,gBAAgB,KAAK,SAAS;AAAA,MACrD,OACK;AACJ,0BAAkB,IAAI,gBAAgB,GAAG;AAAA,MAC1C;AASA,aAAO;AAAA,IACR;AACA,QAAI,iBAAiB;AACpB,OAAC,cAAc,QAAQ,WAAW,QAAQ,EAAE,QAAQ,SAAS,MAAM;AAClE,eAAO,eAAe,cAAc,MAAM;AAAA,UACzC,KAAK,WAAW;AAAE,mBAAO,gBAAgB,IAAI;AAAA,UAAG;AAAA,QACjD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAKA,WAAO,UAAU;AAAA,MACb,gBAAiB,kBAAkB,eAAe;AAAA,MAClD,WAAiB;AAAA,IACrB;AAAA;AAAA;;;ACrDA;AAAA;AAQA,KAAC,WAAW;AACV,UAAI,OAAO,KAAK,SAAS,QAAQ,SAAS;AAE1C,cAAQ;AAER,YAAM;AAEN,YAAM,MAAM,cAAc,SAAS,UAAU,GAAG;AAC9C,eAAO,YAAY,GAAG,QAAQ;AAAA,MAChC;AAEA,YAAM,MAAM,gBAAgB,SAAS,IAAI;AACvC,eAAO,cAAc,EAAE;AAAA,MACzB;AAEA,gBAAU,SAAS,MAAM,MAAM;AAC7B,YAAI,QAAQ;AACZ,iBAAS;AACT,aAAK;AAAA,UACH,KAAK,YAAY,OAAO,MAAM;AAAA,UAC9B,MAAM,SAAS,GAAG;AAChB,mBAAO,OAAO,MAAM,CAAC;AAAA,UACvB;AAAA,UACA,OAAO,WAAW;AAChB,mBAAO,OAAO,IAAI;AAAA,UACpB;AAAA,QACF;AACA,iBAAS,IAAI,QAAQ,MAAM,MAAM,SAAS,GAAG;AAC3C,iBAAO,GAAG,OAAO;AAAA,QACnB,CAAC;AACD,eAAO,GAAG,SAAS,SAAS,GAAG;AAC7B,iBAAO,OAAO,GAAG,YAAY,aAAa,GAAG,QAAQ,CAAC,IAAI;AAAA,QAC5D,CAAC;AACD,eAAO,GAAG,SAAS,SAAS,GAAG;AAC7B,iBAAO,OAAO,GAAG,YAAY,aAAa,GAAG,QAAQ,CAAC,IAAI;AAAA,QAC5D,CAAC;AACD,eAAO,GAAG,QAAQ,SAAS,MAAM;AAC/B,cAAI;AACJ,kBAAQ;AAAA,YACN,QAAQ,KAAK,SAAS;AAAA,UACxB;AACA,iBAAO,GAAG,UAAU,KAAK;AAAA,QAC3B,CAAC;AACD,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,KAAK;AACrB,YAAI,iBAAiB,YAAY,QAAQ;AACzC,0BAAkB,kBAAqB;AACvC,qBAAa;AACb,aAAK;AAAA,UACH;AAAA,UACA,MAAM,SAAS,GAAG;AAChB,mBAAO,WAAW,QAAQ,CAAC;AAAA,UAC7B;AAAA,UACA,OAAO,WAAW;AAChB,mBAAO,WAAW,MAAM;AAAA,UAC1B;AAAA,QACF;AACA,iBAAS,IAAI,gBAAgB;AAC7B,eAAO,GAAG,WAAW,SAAS,MAAM;AAClC,uBAAa;AACb,aAAG,OAAO;AACV,qBAAW,GAAG,SAAS,SAAS,OAAO;AACrC,mBAAO,OAAO,GAAG,YAAY,aAAa,GAAG,QAAQ,KAAK,IAAI;AAAA,UAChE,CAAC;AACD,qBAAW,GAAG,SAAS,WAAW;AAChC,mBAAO,OAAO,GAAG,YAAY,aAAa,GAAG,QAAQ,IAAI;AAAA,UAC3D,CAAC;AACD,iBAAO,WAAW,GAAG,WAAW,SAAS,SAAS;AAChD,gBAAI;AACJ,gBAAI,QAAQ,SAAS,QAAQ;AAC3B,sBAAQ;AAAA,gBACN,QAAQ,QAAQ;AAAA,cAClB;AACA,qBAAO,GAAG,UAAU,KAAK;AAAA,YAC3B;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AACD,eAAO,QAAQ,GAAG;AAClB,eAAO;AAAA,MACT;AAEA,gBAAU,SAAS,MAAM,MAAM;AAC7B,YAAI;AACJ,iBAAS,QAAQ,MAAM,IAAI;AAC3B,eAAO,MAAM,MAAM,KAAK,MAAM;AAAA,MAChC;AAEA,eAAS,SAAS,KAAK;AACrB,YAAI;AACJ,iBAAS,OAAO,GAAG;AACnB,eAAO,MAAM,MAAM,KAAK,MAAM;AAAA,MAChC;AAEA,cAAQ,UAAU;AAElB,cAAQ,SAAS;AAAA,IAEnB,GAAG,KAAK,OAAI;AAAA;AAAA;;;AC3GZ;AAAA;AASA,QAAI,QAAQ;AACZ,QAAI,YAAY;AAEhB,WAAO,UAAU,MAAM;AACvB,WAAO,QAAQ,UAAU,UAAU;AACnC,WAAO,QAAQ,SAAS,UAAU;AAAA;AAAA;", "names": ["<PERSON>ame", "Client", "_ref1", "_i", "_len", "_results", "headers"]}