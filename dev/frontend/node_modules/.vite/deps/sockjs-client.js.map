{"version": 3, "sources": ["../../sockjs-client/lib/utils/browser-crypto.js", "../../sockjs-client/lib/utils/random.js", "../../sockjs-client/lib/utils/event.js", "../../requires-port/index.js", "../../querystringify/index.js", "../../url-parse/index.js", "../../ms/index.js", "../../debug/src/common.js", "../../debug/src/browser.js", "../../sockjs-client/lib/utils/url.js", "../../inherits/inherits_browser.js", "../../sockjs-client/lib/event/eventtarget.js", "../../sockjs-client/lib/event/emitter.js", "../../sockjs-client/lib/transport/browser/websocket.js", "../../sockjs-client/lib/transport/websocket.js", "../../sockjs-client/lib/transport/lib/buffered-sender.js", "../../sockjs-client/lib/transport/lib/polling.js", "../../sockjs-client/lib/transport/lib/sender-receiver.js", "../../sockjs-client/lib/transport/lib/ajax-based.js", "../../sockjs-client/lib/transport/receiver/xhr.js", "../../sockjs-client/lib/transport/browser/abstract-xhr.js", "../../sockjs-client/lib/transport/sender/xhr-cors.js", "../../sockjs-client/lib/transport/sender/xhr-local.js", "../../sockjs-client/lib/utils/browser.js", "../../sockjs-client/lib/transport/xhr-streaming.js", "../../sockjs-client/lib/transport/sender/xdr.js", "../../sockjs-client/lib/transport/xdr-streaming.js", "../../sockjs-client/lib/transport/browser/eventsource.js", "../../sockjs-client/lib/transport/receiver/eventsource.js", "../../sockjs-client/lib/transport/eventsource.js", "../../sockjs-client/lib/version.js", "../../sockjs-client/lib/utils/iframe.js", "../../sockjs-client/lib/transport/iframe.js", "../../sockjs-client/lib/utils/object.js", "../../sockjs-client/lib/transport/lib/iframe-wrap.js", "../../sockjs-client/lib/transport/receiver/htmlfile.js", "../../sockjs-client/lib/transport/htmlfile.js", "../../sockjs-client/lib/transport/xhr-polling.js", "../../sockjs-client/lib/transport/xdr-polling.js", "../../sockjs-client/lib/transport/receiver/jsonp.js", "../../sockjs-client/lib/transport/sender/jsonp.js", "../../sockjs-client/lib/transport/jsonp-polling.js", "../../sockjs-client/lib/transport-list.js", "../../sockjs-client/lib/shims.js", "../../sockjs-client/lib/utils/escape.js", "../../sockjs-client/lib/utils/transport.js", "../../sockjs-client/lib/utils/log.js", "../../sockjs-client/lib/event/event.js", "../../sockjs-client/lib/location.js", "../../sockjs-client/lib/event/close.js", "../../sockjs-client/lib/event/trans-message.js", "../../sockjs-client/lib/transport/sender/xhr-fake.js", "../../sockjs-client/lib/info-ajax.js", "../../sockjs-client/lib/info-iframe-receiver.js", "../../sockjs-client/lib/info-iframe.js", "../../sockjs-client/lib/info-receiver.js", "../../sockjs-client/lib/facade.js", "../../sockjs-client/lib/iframe-bootstrap.js", "../../sockjs-client/lib/main.js", "../../sockjs-client/lib/entry.js"], "sourcesContent": ["'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Array(length);\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n    return bytes;\n  };\n}\n", "'use strict';\n\nvar crypto = require('crypto');\n\n// This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function(length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n    return ret.join('');\n  }\n\n, number: function(max) {\n    return Math.floor(Math.random() * max);\n  }\n\n, numberString: function(max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};\n", "'use strict';\n\nvar random = require('./random');\n\nvar onUnload = {}\n  , afterUnload = false\n    // detect google chrome packaged apps because they don't allow the 'unload' event\n  , isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime\n  ;\n\nmodule.exports = {\n  attachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  }\n\n, detachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  }\n\n, unloadAdd: function(listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  }\n\n, unloadDel: function(ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  }\n\n, triggerUnloadCallbacks: function() {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\n\nvar unloadTriggered = function() {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}\n", "'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n    return port !== 80;\n\n    case 'https':\n    case 'wss':\n    return port !== 443;\n\n    case 'ftp':\n    return port !== 21;\n\n    case 'gopher':\n    return port !== 70;\n\n    case 'file':\n    return false;\n  }\n\n  return port !== 0;\n};\n", "'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g\n    , result = {}\n    , part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1])\n      , value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n\n  var pairs = []\n    , value\n    , key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encode(key);\n      value = encode(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key +'='+ value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;\n", "'use strict';\n\nvar required = require('requires-port')\n  , qs = require('querystringify')\n  , controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/\n  , CRHTLF = /[\\n\\r\\t]/g\n  , slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//\n  , port = /:\\d+$/\n  , protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i\n  , windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [\n  ['#', 'hash'],                        // Extract from the back.\n  ['?', 'query'],                       // Extract from the back.\n  function sanitize(address, url) {     // Sanitize what is left of the address\n    return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n  },\n  ['/', 'pathname'],                    // Extract from the back.\n  ['@', 'auth', 1],                     // Extract from the front.\n  [NaN, 'host', undefined, 1, 1],       // Set left over value.\n  [/:(\\d*)$/, 'port', undefined, 1],    // RegExp the back.\n  [NaN, 'hostname', undefined, 1, 1]    // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = { hash: 1, query: 1 };\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n\n  if (typeof window !== 'undefined') globalVar = window;\n  else if (typeof global !== 'undefined') globalVar = global;\n  else if (typeof self !== 'undefined') globalVar = self;\n  else globalVar = {};\n\n  var location = globalVar.location || {};\n  loc = loc || location;\n\n  var finaldestination = {}\n    , type = typeof loc\n    , key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return (\n    scheme === 'file:' ||\n    scheme === 'ftp:' ||\n    scheme === 'http:' ||\n    scheme === 'https:' ||\n    scheme === 'ws:' ||\n    scheme === 'wss:'\n  );\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4]\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/'))\n    , i = path.length\n    , last = path[i - 1]\n    , unshift = false\n    , up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative, extracted, parse, instruction, index, key\n    , instructions = rules.slice()\n    , type = typeof location\n    , url = this\n    , i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (\n    extracted.protocol === 'file:' && (\n      extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) ||\n    (!extracted.slashes &&\n      (extracted.protocol ||\n        extracted.slashesCount < 2 ||\n        !isSpecial(url.protocol)))\n  ) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@'\n        ? address.lastIndexOf(parse)\n        : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if ((index = parse.exec(address))) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (\n      relative && instruction[3] ? location[key] || '' : ''\n    );\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (\n      relative\n    && location.slashes\n    && url.pathname.charAt(0) !== '/'\n    && (url.pathname !== '' || location.pathname !== '')\n  ) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password))\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username +':'+ url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname +':'+ value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n\n      if (url.port) value += ':'+ url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username +':'+ url.password : url.username;\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  url.href = url.toString();\n\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n\n  var query\n    , url = this\n    , host = url.host\n    , protocol = url.protocol;\n\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n\n  var result =\n    protocol +\n    ((url.protocol && url.slashes) || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':'+ url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':'+ url.password;\n    result += '@';\n  } else if (\n    url.protocol !== 'file:' &&\n    isSpecial(url.protocol) &&\n    !host &&\n    url.pathname !== '/'\n  ) {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || (port.test(url.hostname) && !url.port)) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?'+ query : query;\n\n  if (url.hash) result += url.hash;\n\n  return result;\n}\n\nUrl.prototype = { set: set, toString: toString };\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\n\nmodule.exports = Url;\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\"use strict\";\n\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\nfunction setup(env) {\n  createDebug.debug = createDebug;\n  createDebug.default = createDebug;\n  createDebug.coerce = coerce;\n  createDebug.disable = disable;\n  createDebug.enable = enable;\n  createDebug.enabled = enabled;\n  createDebug.humanize = require('ms');\n  Object.keys(env).forEach(function (key) {\n    createDebug[key] = env[key];\n  });\n  /**\n  * Active `debug` instances.\n  */\n\n  createDebug.instances = [];\n  /**\n  * The currently active debug mode names, and names to skip.\n  */\n\n  createDebug.names = [];\n  createDebug.skips = [];\n  /**\n  * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n  *\n  * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n  */\n\n  createDebug.formatters = {};\n  /**\n  * Selects a color for a debug namespace\n  * @param {String} namespace The namespace string for the for the debug instance to be colored\n  * @return {Number|String} An ANSI color code for the given namespace\n  * @api private\n  */\n\n  function selectColor(namespace) {\n    var hash = 0;\n\n    for (var i = 0; i < namespace.length; i++) {\n      hash = (hash << 5) - hash + namespace.charCodeAt(i);\n      hash |= 0; // Convert to 32bit integer\n    }\n\n    return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n  }\n\n  createDebug.selectColor = selectColor;\n  /**\n  * Create a debugger with the given `namespace`.\n  *\n  * @param {String} namespace\n  * @return {Function}\n  * @api public\n  */\n\n  function createDebug(namespace) {\n    var prevTime;\n\n    function debug() {\n      // Disabled?\n      if (!debug.enabled) {\n        return;\n      }\n\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      var self = debug; // Set `diff` timestamp\n\n      var curr = Number(new Date());\n      var ms = curr - (prevTime || curr);\n      self.diff = ms;\n      self.prev = prevTime;\n      self.curr = curr;\n      prevTime = curr;\n      args[0] = createDebug.coerce(args[0]);\n\n      if (typeof args[0] !== 'string') {\n        // Anything else let's inspect with %O\n        args.unshift('%O');\n      } // Apply any `formatters` transformations\n\n\n      var index = 0;\n      args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n        // If we encounter an escaped % then don't increase the array index\n        if (match === '%%') {\n          return match;\n        }\n\n        index++;\n        var formatter = createDebug.formatters[format];\n\n        if (typeof formatter === 'function') {\n          var val = args[index];\n          match = formatter.call(self, val); // Now we need to remove `args[index]` since it's inlined in the `format`\n\n          args.splice(index, 1);\n          index--;\n        }\n\n        return match;\n      }); // Apply env-specific formatting (colors, etc.)\n\n      createDebug.formatArgs.call(self, args);\n      var logFn = self.log || createDebug.log;\n      logFn.apply(self, args);\n    }\n\n    debug.namespace = namespace;\n    debug.enabled = createDebug.enabled(namespace);\n    debug.useColors = createDebug.useColors();\n    debug.color = selectColor(namespace);\n    debug.destroy = destroy;\n    debug.extend = extend; // Debug.formatArgs = formatArgs;\n    // debug.rawLog = rawLog;\n    // env-specific initialization logic for debug instances\n\n    if (typeof createDebug.init === 'function') {\n      createDebug.init(debug);\n    }\n\n    createDebug.instances.push(debug);\n    return debug;\n  }\n\n  function destroy() {\n    var index = createDebug.instances.indexOf(this);\n\n    if (index !== -1) {\n      createDebug.instances.splice(index, 1);\n      return true;\n    }\n\n    return false;\n  }\n\n  function extend(namespace, delimiter) {\n    return createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n  }\n  /**\n  * Enables a debug mode by namespaces. This can include modes\n  * separated by a colon and wildcards.\n  *\n  * @param {String} namespaces\n  * @api public\n  */\n\n\n  function enable(namespaces) {\n    createDebug.save(namespaces);\n    createDebug.names = [];\n    createDebug.skips = [];\n    var i;\n    var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n    var len = split.length;\n\n    for (i = 0; i < len; i++) {\n      if (!split[i]) {\n        // ignore empty strings\n        continue;\n      }\n\n      namespaces = split[i].replace(/\\*/g, '.*?');\n\n      if (namespaces[0] === '-') {\n        createDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n      } else {\n        createDebug.names.push(new RegExp('^' + namespaces + '$'));\n      }\n    }\n\n    for (i = 0; i < createDebug.instances.length; i++) {\n      var instance = createDebug.instances[i];\n      instance.enabled = createDebug.enabled(instance.namespace);\n    }\n  }\n  /**\n  * Disable debug output.\n  *\n  * @api public\n  */\n\n\n  function disable() {\n    createDebug.enable('');\n  }\n  /**\n  * Returns true if the given mode name is enabled, false otherwise.\n  *\n  * @param {String} name\n  * @return {Boolean}\n  * @api public\n  */\n\n\n  function enabled(name) {\n    if (name[name.length - 1] === '*') {\n      return true;\n    }\n\n    var i;\n    var len;\n\n    for (i = 0, len = createDebug.skips.length; i < len; i++) {\n      if (createDebug.skips[i].test(name)) {\n        return false;\n      }\n    }\n\n    for (i = 0, len = createDebug.names.length; i < len; i++) {\n      if (createDebug.names[i].test(name)) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n  /**\n  * Coerce `val`.\n  *\n  * @param {Mixed} val\n  * @return {Mixed}\n  * @api private\n  */\n\n\n  function coerce(val) {\n    if (val instanceof Error) {\n      return val.stack || val.message;\n    }\n\n    return val;\n  }\n\n  createDebug.enable(createDebug.load());\n  return createDebug;\n}\n\nmodule.exports = setup;\n\n", "\"use strict\";\n\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\n/**\n * Colors.\n */\n\nexports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n// eslint-disable-next-line complexity\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n    return true;\n  } // Internet Explorer and Edge do not support colors.\n\n\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  } // Is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\n\n  return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n  typeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) || // Is firefox >= v31?\n  // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\n\nfunction formatArgs(args) {\n  args[0] = (this.useColors ? '%c' : '') + this.namespace + (this.useColors ? ' %c' : ' ') + args[0] + (this.useColors ? '%c ' : ' ') + '+' + module.exports.humanize(this.diff);\n\n  if (!this.useColors) {\n    return;\n  }\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit'); // The final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function (match) {\n    if (match === '%%') {\n      return;\n    }\n\n    index++;\n\n    if (match === '%c') {\n      // We only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n  args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\n\nfunction log() {\n  var _console;\n\n  // This hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return (typeof console === \"undefined\" ? \"undefined\" : _typeof(console)) === 'object' && console.log && (_console = console).log.apply(_console, arguments);\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\n\nfunction save(namespaces) {\n  try {\n    if (namespaces) {\n      exports.storage.setItem('debug', namespaces);\n    } else {\n      exports.storage.removeItem('debug');\n    }\n  } catch (error) {// Swallow\n    // XXX (@Qix-) should we be logging these?\n  }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\n\nfunction load() {\n  var r;\n\n  try {\n    r = exports.storage.getItem('debug');\n  } catch (error) {} // Swallow\n  // XXX (@Qix-) should we be logging these?\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\n\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\n\nfunction localstorage() {\n  try {\n    // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n    // The Browser also has localStorage in the global context.\n    return localStorage;\n  } catch (error) {// Swallow\n    // XXX (@Qix-) should we be logging these?\n  }\n}\n\nmodule.exports = require('./common')(exports);\nvar formatters = module.exports.formatters;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n  try {\n    return JSON.stringify(v);\n  } catch (error) {\n    return '[UnexpectedJSONParseError]: ' + error.message;\n  }\n};\n\n", "'use strict';\n\nvar URL = require('url-parse');\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\n\nmodule.exports = {\n  getOrigin: function(url) {\n    if (!url) {\n      return null;\n    }\n\n    var p = new URL(url);\n    if (p.protocol === 'file:') {\n      return null;\n    }\n\n    var port = p.port;\n    if (!port) {\n      port = (p.protocol === 'https:') ? '443' : '80';\n    }\n\n    return p.protocol + '//' + p.hostname + ':' + port;\n  }\n\n, isOriginEqual: function(a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  }\n\n, isSchemeEqual: function(a, b) {\n    return (a.split(':')[0] === b.split(':')[0]);\n  }\n\n, addPath: function (url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  }\n\n, addQuery: function (url, q) {\n    return url + (url.indexOf('?') === -1 ? ('?' + q) : ('&' + q));\n  }\n\n, isLoopbackAddr: function (addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "'use strict';\n\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\n\nfunction EventTarget() {\n  this._listeners = {};\n}\n\nEventTarget.prototype.addEventListener = function(eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n  var arr = this._listeners[eventType];\n  // #4\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n  this._listeners[eventType] = arr;\n};\n\nEventTarget.prototype.removeEventListener = function(eventType, listener) {\n  var arr = this._listeners[eventType];\n  if (!arr) {\n    return;\n  }\n  var idx = arr.indexOf(listener);\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n    return;\n  }\n};\n\nEventTarget.prototype.dispatchEvent = function() {\n  var event = arguments[0];\n  var t = event.type;\n  // equivalent of Array.prototype.slice.call(arguments, 0);\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments);\n  // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\n\nmodule.exports = EventTarget;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventTarget = require('./eventtarget')\n  ;\n\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\n\ninherits(EventEmitter, EventTarget);\n\nEventEmitter.prototype.removeAllListeners = function(type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\n\nEventEmitter.prototype.once = function(type, listener) {\n  var self = this\n    , fired = false;\n\n  function g() {\n    self.removeListener(type, g);\n\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n\n  this.on(type, g);\n};\n\nEventEmitter.prototype.emit = function() {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\n\nmodule.exports.EventEmitter = EventEmitter;\n", "'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\nif (Driver) {\n\tmodule.exports = function WebSocketBrowserDriver(url) {\n\t\treturn new Driver(url);\n\t};\n} else {\n\tmodule.exports = undefined;\n}\n", "'use strict';\n\nvar utils = require('../utils/event')\n  , urlUtils = require('../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , WebsocketDriver = require('./driver/websocket')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\n\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function(e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function(e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function(e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\n\ninherits(WebSocketTransport, EventEmitter);\n\nWebSocketTransport.prototype.send = function(data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\n\nWebSocketTransport.prototype.close = function() {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\n\nWebSocketTransport.prototype._cleanup = function() {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\n\nWebSocketTransport.enabled = function() {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\n\nmodule.exports = WebSocketTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\n\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\n\ninherits(BufferedSender, EventEmitter);\n\nBufferedSender.prototype.send = function(message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n};\n\n// For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\nBufferedSender.prototype.sendScheduleWait = function() {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n  this.sendStop = function() {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n  tref = setTimeout(function() {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\n\nBufferedSender.prototype.sendSchedule = function() {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function(err) {\n      self.sendStop = null;\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\n\nBufferedSender.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nBufferedSender.prototype.close = function() {\n  debug('close');\n  this._cleanup();\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\n\nmodule.exports = BufferedSender;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\n\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\n\ninherits(Polling, EventEmitter);\n\nPolling.prototype._scheduleReceiver = function() {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n\n  poll.on('message', function(msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n\n  poll.once('close', function(code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\n\nPolling.prototype.abort = function() {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\n\nmodule.exports = Polling;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , BufferedSender = require('./buffered-sender')\n  , Polling = require('./polling')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\n\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function(msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function(code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\n\ninherits(SenderReceiver, BufferedSender);\n\nSenderReceiver.prototype.close = function() {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\n\nmodule.exports = SenderReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , SenderReceiver = require('./sender-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\n\nfunction createAjaxSender(AjaxObject) {\n  return function(url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {'Content-type': 'text/plain'};\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function(status) {\n      debug('finish', status);\n      xo = null;\n\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function() {\n      debug('abort');\n      xo.close();\n      xo = null;\n\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\n\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\n\ninherits(AjaxBasedTransport, SenderReceiver);\n\nmodule.exports = AjaxBasedTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\n\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n\n  this.bufferPosition = 0;\n\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function(status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\n\ninherits(XhrReceiver, EventEmitter);\n\nXhrReceiver.prototype._chunkHandler = function(status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n\n  for (var idx = -1; ; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\n\nXhrReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nXhrReceiver.prototype.abort = function() {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\n\nmodule.exports = XhrReceiver;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('../../utils/event')\n  , urlUtils = require('../../utils/url')\n  , XHR = global.XMLHttpRequest\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\n\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\n\ninherits(AbstractXHRObject, EventEmitter);\n\nAbstractXHRObject.prototype._start = function(method, url, payload, opts) {\n  var self = this;\n\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function() {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n\n  this.xhr.onreadystatechange = function() {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n      case 3:\n        // IE doesn't like peeking into responseText or status\n        // on Microsoft.XMLHTTP and readystate=3\n        try {\n          status = x.status;\n          text = x.responseText;\n        } catch (e) {\n          // intentionally empty\n        }\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n\n        // IE does return readystate == 3 for 404 answers.\n        if (status === 200 && text && text.length > 0) {\n          debug('chunk');\n          self.emit('chunk', status, text);\n        }\n        break;\n      case 4:\n        status = x.status;\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n        // IE returns this for a bad port\n        // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n        if (status === 12005 || status === 12029) {\n          status = 0;\n        }\n\n        debug('finish', status, x.responseText);\n        self.emit('finish', status, x.responseText);\n        self._cleanup(false);\n        break;\n      }\n    }\n  };\n\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\n\nAbstractXHRObject.prototype._cleanup = function(abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function() {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\n\nAbstractXHRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && (axo in global)) {\n  debug('overriding xmlhttprequest');\n  XHR = function() {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\n\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\n\nAbstractXHRObject.supportsCORS = cors;\n\nmodule.exports = AbstractXHRObject;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\n\ninherits(XHRCorsObject, XhrDriver);\n\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\n\nmodule.exports = XHRCorsObject;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\n\ninherits(XHRLocalObject, XhrDriver);\n\nXHRLocalObject.enabled = XhrDriver.enabled;\n\nmodule.exports = XHRLocalObject;\n", "'use strict';\n\nmodule.exports = {\n  isOpera: function() {\n    return global.navigator &&\n      /opera/i.test(global.navigator.userAgent);\n  }\n\n, isKonqueror: function() {\n    return global.navigator &&\n      /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n, hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , browser = require('../utils/browser')\n  ;\n\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrStreamingTransport, AjaxBasedTransport);\n\nXhrStreamingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n  if (browser.isOpera()) {\n    return false;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\nXhrStreamingTransport.needBody = !!global.document;\n\nmodule.exports = XhrStreamingTransport;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , eventUtils = require('../../utils/event')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n}\n\n// References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self._start(method, url, payload);\n  }, 0);\n}\n\ninherits(XDRObject, EventEmitter);\n\nXDRObject.prototype._start = function(method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest();\n  // IE caches even POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  xdr.onerror = function() {\n    debug('onerror');\n    self._error();\n  };\n  xdr.ontimeout = function() {\n    debug('ontimeout');\n    self._error();\n  };\n  xdr.onprogress = function() {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n  xdr.onload = function() {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n    self._cleanup(false);\n  };\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function() {\n    self._cleanup(true);\n  });\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\n\nXDRObject.prototype._error = function() {\n  this.emit('finish', 0, '');\n  this._cleanup(false);\n};\n\nXDRObject.prototype._cleanup = function(abort) {\n  debug('cleanup', abort);\n  if (!this.xdr) {\n    return;\n  }\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xdr = null;\n};\n\nXDRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\n// IE 8/9 if the request target uses the same scheme - #79\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\n\nmodule.exports = XDRObject;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\n\ninherits(XdrStreamingTransport, AjaxBasedTransport);\n\nXdrStreamingTransport.enabled = function(info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\n\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;\n", "module.exports = global.EventSource;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , EventSourceDriver = require('eventsource')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\n\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function(e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function(e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = (es.readyState !== 2 ? 'network' : 'permanent');\n    self._cleanup();\n    self._close(reason);\n  };\n}\n\ninherits(EventSourceReceiver, EventEmitter);\n\nEventSourceReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nEventSourceReceiver.prototype._cleanup = function() {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\n\nEventSourceReceiver.prototype._close = function(reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function() {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\n\nmodule.exports = EventSourceReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , EventSourceReceiver = require('./receiver/eventsource')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , EventSourceDriver = require('eventsource')\n  ;\n\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\n\ninherits(EventSourceTransport, AjaxBasedTransport);\n\nEventSourceTransport.enabled = function() {\n  return !!EventSourceDriver;\n};\n\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\n\nmodule.exports = EventSourceTransport;\n", "module.exports = '1.6.1';\n", "'use strict';\n\nvar eventUtils = require('./event')\n  , browser = require('./browser')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\n\nmodule.exports = {\n  WPrefix: '_jp'\n, currentWindowId: null\n\n, polluteGlobalNamespace: function() {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  }\n\n, postMessage: function(type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId\n      , type: type\n      , data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  }\n\n, createIframe: function(iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function() {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function() {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function(err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function(msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function() {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    iframe.onload = function() {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function() {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n\n/* eslint no-undef: \"off\", new-cap: \"off\" */\n, createHtmlfile: function(iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function() {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function(r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function(msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function() {\n          if (iframe && iframe.contentWindow) {\n              iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n\n    doc.open();\n    doc.write('<html><s' + 'cript>' +\n              'document.domain=\"' + global.document.domain + '\";' +\n              '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n};\n\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' ||\n    typeof global.postMessage === 'object') && (!browser.isKonqueror());\n}\n", "'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , version = require('../version')\n  , urlUtils = require('../utils/url')\n  , iframeUtils = require('../utils/iframe')\n  , eventUtils = require('../utils/event')\n  , random = require('../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\n\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function(r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\n\ninherits(IframeTransport, EventEmitter);\n\nIframeTransport.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\n\nIframeTransport.prototype._message = function(e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n\n  switch (iframeMessage.type) {\n  case 's':\n    this.iframeObj.loaded();\n    // window global dependency\n    this.postMessage('s', JSON.stringify([\n      version\n    , this.transport\n    , this.transUrl\n    , this.baseUrl\n    ]));\n    break;\n  case 't':\n    this.emit('message', iframeMessage.data);\n    break;\n  case 'c':\n    var cdata;\n    try {\n      cdata = JSON.parse(iframeMessage.data);\n    } catch (ignored) {\n      debug('bad json', iframeMessage.data);\n      return;\n    }\n    this.emit('close', cdata[0], cdata[1]);\n    this.close();\n    break;\n  }\n};\n\nIframeTransport.prototype.postMessage = function(type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId\n  , type: type\n  , data: data || ''\n  }), this.origin);\n};\n\nIframeTransport.prototype.send = function(message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\n\nIframeTransport.enabled = function() {\n  return iframeUtils.iframeEnabled;\n};\n\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\n\nmodule.exports = IframeTransport;\n", "'use strict';\n\nmodule.exports = {\n  isObject: function(obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  }\n\n, extend: function(obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};\n", "'use strict';\n\nvar inherits = require('inherits')\n  , IframeTransport = require('../iframe')\n  , objectUtils = require('../../utils/object')\n  ;\n\nmodule.exports = function(transport) {\n\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n\n  inherits(IframeWrapTransport, IframeTransport);\n\n  IframeWrapTransport.enabled = function(url, info) {\n    if (!global.document) {\n      return false;\n    }\n\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n\n  return IframeWrapTransport;\n};\n", "'use strict';\n\nvar inherits = require('inherits')\n  , iframeUtils = require('../../utils/iframe')\n  , urlUtils = require('../../utils/url')\n  , EventEmitter = require('events').EventEmitter\n  , random = require('../../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\n\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ?\n      iframeUtils.createHtmlfile : iframeUtils.createIframe;\n\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function() {\n      debug('start');\n      self.iframeObj.loaded();\n    }\n  , message: function(data) {\n      debug('message', data);\n      self.emit('message', data);\n    }\n  , stop: function() {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function() {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\n\ninherits(HtmlfileReceiver, EventEmitter);\n\nHtmlfileReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nHtmlfileReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\n\nHtmlfileReceiver.prototype._close = function(reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\n\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\n\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\n\nmodule.exports = HtmlfileReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , HtmlfileReceiver = require('./receiver/htmlfile')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  ;\n\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\n\ninherits(HtmlFileTransport, AjaxBasedTransport);\n\nHtmlFileTransport.enabled = function(info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\n\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\n\nmodule.exports = HtmlFileTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  ;\n\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrPollingTransport, AjaxBasedTransport);\n\nXhrPollingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\n\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XdrStreamingTransport = require('./xdr-streaming')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\n\ninherits(XdrPollingTransport, AjaxBasedTransport);\n\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;\n", "'use strict';\n\nvar utils = require('../../utils/iframe')\n  , random = require('../../utils/random')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\n\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n\n  utils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n  this._createScript(urlWithId);\n\n  // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n  this.timeoutId = setTimeout(function() {\n    debug('timeout');\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\n\ninherits(JsonpReceiver, EventEmitter);\n\nJsonpReceiver.prototype.abort = function() {\n  debug('abort');\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n    this._abort(err);\n  }\n};\n\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\n\nJsonpReceiver.prototype._callback = function(data) {\n  debug('_callback', data);\n  this._cleanup();\n\n  if (this.aborting) {\n    return;\n  }\n\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._abort = function(err) {\n  debug('_abort', err);\n  this._cleanup();\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n  if (this.script) {\n    var script = this.script;\n    // Unfortunately, you can't really abort script loading of\n    // the script.\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror =\n        script.onload = script.onclick = null;\n    this.script = null;\n  }\n  delete global[utils.WPrefix][this.id];\n};\n\nJsonpReceiver.prototype._scriptError = function() {\n  debug('_scriptError');\n  var self = this;\n  if (this.errorTimer) {\n    return;\n  }\n\n  this.errorTimer = setTimeout(function() {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\n\nJsonpReceiver.prototype._createScript = function(url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2;  // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n  script.onload = function() {\n    debug('onload');\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  };\n\n  // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n  script.onreadystatechange = function() {\n    debug('onreadystatechange', script.readyState);\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {\n          // intentionally empty\n        }\n      }\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  };\n  // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {\n        // intentionally empty\n      }\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\n\nmodule.exports = JsonpReceiver;\n", "'use strict';\n\nvar random = require('../../utils/random')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\n\nvar form, area;\n\nfunction createIframe(id) {\n  debug('createIframe', id);\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\n\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n\n  global.document.body.appendChild(form);\n}\n\nmodule.exports = function(url, payload, callback) {\n  debug(url, payload);\n  if (!form) {\n    createForm();\n  }\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n\n  try {\n    area.value = payload;\n  } catch (e) {\n    // seriously broken browsers get here\n  }\n  form.submit();\n\n  var completed = function(err) {\n    debug('completed', id, err);\n    if (!iframe.onerror) {\n      return;\n    }\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null;\n    // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n    setTimeout(function() {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = '';\n    // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n    callback(err);\n  };\n  iframe.onerror = function() {\n    debug('onerror', id);\n    completed();\n  };\n  iframe.onload = function() {\n    debug('onload', id);\n    completed();\n  };\n  iframe.onreadystatechange = function(e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n  return function() {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};\n", "'use strict';\n\n// The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\n\nvar inherits = require('inherits')\n  , SenderReceiver = require('./lib/sender-receiver')\n  , JsonpReceiver = require('./receiver/jsonp')\n  , jsonpSender = require('./sender/jsonp')\n  ;\n\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\n\ninherits(JsonPTransport, SenderReceiver);\n\nJsonPTransport.enabled = function() {\n  return !!global.document;\n};\n\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\n\nmodule.exports = JsonPTransport;\n", "'use strict';\n\nmodule.exports = [\n  // streaming transports\n  require('./transport/websocket')\n, require('./transport/xhr-streaming')\n, require('./transport/xdr-streaming')\n, require('./transport/eventsource')\n, require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n  // polling transports\n, require('./transport/htmlfile')\n, require('./transport/lib/iframe-wrap')(require('./transport/htmlfile'))\n, require('./transport/xhr-polling')\n, require('./transport/xdr-polling')\n, require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling'))\n, require('./transport/jsonp-polling')\n];\n", "/* eslint-disable */\n/* jscs: disable */\n'use strict';\n\n// pulled specific shims from https://github.com/es-shims/es5-shim\n\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\n\nvar _toString = ObjectPrototype.toString;\nvar isFunction = function (val) {\n    return ObjectPrototype.toString.call(val) === '[object Function]';\n};\nvar isArray = function isArray(obj) {\n    return _toString.call(obj) === '[object Array]';\n};\nvar isString = function isString(obj) {\n    return _toString.call(obj) === '[object String]';\n};\n\nvar supportsDescriptors = Object.defineProperty && (function () {\n    try {\n        Object.defineProperty({}, 'x', {});\n        return true;\n    } catch (e) { /* this is ES3 */\n        return false;\n    }\n}());\n\n// Define configurable, writable and non-enumerable props\n// if they don't exist.\nvar defineProperty;\nif (supportsDescriptors) {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        Object.defineProperty(object, name, {\n            configurable: true,\n            enumerable: false,\n            writable: true,\n            value: method\n        });\n    };\n} else {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        object[name] = method;\n    };\n}\nvar defineProperties = function (object, map, forceAssign) {\n    for (var name in map) {\n        if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n          defineProperty(object, name, map[name], forceAssign);\n        }\n    }\n};\n\nvar toObject = function (o) {\n    if (o == null) { // this matches both null and undefined\n        throw new TypeError(\"can't convert \" + o + ' to object');\n    }\n    return Object(o);\n};\n\n//\n// Util\n// ======\n//\n\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\nfunction toInteger(num) {\n    var n = +num;\n    if (n !== n) { // isNaN\n        n = 0;\n    } else if (n !== 0 && n !== (1 / 0) && n !== -(1 / 0)) {\n        n = (n > 0 || -1) * Math.floor(Math.abs(n));\n    }\n    return n;\n}\n\nfunction ToUint32(x) {\n    return x >>> 0;\n}\n\n//\n// Function\n// ========\n//\n\n// ES-5 ********\n// http://es5.github.com/#x********\n\nfunction Empty() {}\n\ndefineProperties(FunctionPrototype, {\n    bind: function bind(that) { // .length is 1\n        // 1. Let Target be the this value.\n        var target = this;\n        // 2. If IsCallable(Target) is false, throw a TypeError exception.\n        if (!isFunction(target)) {\n            throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n        }\n        // 3. Let A be a new (possibly empty) internal list of all of the\n        //   argument values provided after thisArg (arg1, arg2 etc), in order.\n        // XXX slicedArgs will stand in for \"A\" if used\n        var args = array_slice.call(arguments, 1); // for normal call\n        // 4. Let F be a new native ECMAScript object.\n        // 11. Set the [[Prototype]] internal property of F to the standard\n        //   built-in Function prototype object as specified in ********.\n        // 12. Set the [[Call]] internal property of F as described in\n        //   ********.1.\n        // 13. Set the [[Construct]] internal property of F as described in\n        //   ********.2.\n        // 14. Set the [[HasInstance]] internal property of F as described in\n        //   ********.3.\n        var binder = function () {\n\n            if (this instanceof bound) {\n                // ********.2 [[Construct]]\n                // When the [[Construct]] internal method of a function object,\n                // F that was created using the bind function is called with a\n                // list of arguments ExtraArgs, the following steps are taken:\n                // 1. Let target be the value of F's [[TargetFunction]]\n                //   internal property.\n                // 2. If target has no [[Construct]] internal method, a\n                //   TypeError exception is thrown.\n                // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Construct]] internal\n                //   method of target providing args as the arguments.\n\n                var result = target.apply(\n                    this,\n                    args.concat(array_slice.call(arguments))\n                );\n                if (Object(result) === result) {\n                    return result;\n                }\n                return this;\n\n            } else {\n                // ********.1 [[Call]]\n                // When the [[Call]] internal method of a function object, F,\n                // which was created using the bind function is called with a\n                // this value and a list of arguments ExtraArgs, the following\n                // steps are taken:\n                // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 2. Let boundThis be the value of F's [[BoundThis]] internal\n                //   property.\n                // 3. Let target be the value of F's [[TargetFunction]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Call]] internal method\n                //   of target providing boundThis as the this value and\n                //   providing args as the arguments.\n\n                // equiv: target.call(this, ...boundArgs, ...args)\n                return target.apply(\n                    that,\n                    args.concat(array_slice.call(arguments))\n                );\n\n            }\n\n        };\n\n        // 15. If the [[Class]] internal property of Target is \"Function\", then\n        //     a. Let L be the length property of Target minus the length of A.\n        //     b. Set the length own property of F to either 0 or L, whichever is\n        //       larger.\n        // 16. Else set the length own property of F to 0.\n\n        var boundLength = Math.max(0, target.length - args.length);\n\n        // 17. Set the attributes of the length own property of F to the values\n        //   specified in 15.3.5.1.\n        var boundArgs = [];\n        for (var i = 0; i < boundLength; i++) {\n            boundArgs.push('$' + i);\n        }\n\n        // XXX Build a dynamic function with desired amount of arguments is the only\n        // way to set the length property of a function.\n        // In environments where Content Security Policies enabled (Chrome extensions,\n        // for ex.) all use of eval or Function costructor throws an exception.\n        // However in all of these environments Function.prototype.bind exists\n        // and so this code will never be executed.\n        var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n\n        if (target.prototype) {\n            Empty.prototype = target.prototype;\n            bound.prototype = new Empty();\n            // Clean up dangling references.\n            Empty.prototype = null;\n        }\n\n        // TODO\n        // 18. Set the [[Extensible]] internal property of F to true.\n\n        // TODO\n        // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n        // 20. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n        //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n        //   false.\n        // 21. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n        //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n        //   and false.\n\n        // TODO\n        // NOTE Function objects created using Function.prototype.bind do not\n        // have a prototype property or the [[Code]], [[FormalParameters]], and\n        // [[Scope]] internal properties.\n        // XXX can't delete prototype in pure-js.\n\n        // 22. Return F.\n        return bound;\n    }\n});\n\n//\n// Array\n// =====\n//\n\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\ndefineProperties(Array, { isArray: isArray });\n\n\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\nvar properlyBoxesContext = function properlyBoxed(method) {\n    // Check node 0.6.21 bug where third parameter is not boxed\n    var properlyBoxesNonStrict = true;\n    var properlyBoxesStrict = true;\n    if (method) {\n        method.call('foo', function (_, __, context) {\n            if (typeof context !== 'object') { properlyBoxesNonStrict = false; }\n        });\n\n        method.call([1], function () {\n            'use strict';\n            properlyBoxesStrict = typeof this === 'string';\n        }, 'x');\n    }\n    return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\n\ndefineProperties(ArrayPrototype, {\n    forEach: function forEach(fun /*, thisp*/) {\n        var object = toObject(this),\n            self = splitString && isString(this) ? this.split('') : object,\n            thisp = arguments[1],\n            i = -1,\n            length = self.length >>> 0;\n\n        // If no callback function or if callback is not a callable function\n        if (!isFunction(fun)) {\n            throw new TypeError(); // TODO message\n        }\n\n        while (++i < length) {\n            if (i in self) {\n                // Invoke the callback function with call, passing arguments:\n                // context, property value, property key, thisArg object\n                // context\n                fun.call(thisp, self[i], i, object);\n            }\n        }\n    }\n}, !properlyBoxesContext(ArrayPrototype.forEach));\n\n// ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n    indexOf: function indexOf(sought /*, fromIndex */ ) {\n        var self = splitString && isString(this) ? this.split('') : toObject(this),\n            length = self.length >>> 0;\n\n        if (!length) {\n            return -1;\n        }\n\n        var i = 0;\n        if (arguments.length > 1) {\n            i = toInteger(arguments[1]);\n        }\n\n        // handle negative indices\n        i = i >= 0 ? i : Math.max(0, length + i);\n        for (; i < length; i++) {\n            if (i in self && self[i] === sought) {\n                return i;\n            }\n        }\n        return -1;\n    }\n}, hasFirefox2IndexOfBug);\n\n//\n// String\n// ======\n//\n\n// ES5 *********\n// http://es5.github.com/#x*********\n\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\nif (\n    'ab'.split(/(?:ab)*/).length !== 2 ||\n    '.'.split(/(.?)(.?)/).length !== 4 ||\n    'tesst'.split(/(s)*/)[1] === 't' ||\n    'test'.split(/(?:)/, -1).length !== 4 ||\n    ''.split(/.?/).length ||\n    '.'.split(/()()/).length > 1\n) {\n    (function () {\n        var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n        StringPrototype.split = function (separator, limit) {\n            var string = this;\n            if (separator === void 0 && limit === 0) {\n                return [];\n            }\n\n            // If `separator` is not a regex, use native split\n            if (_toString.call(separator) !== '[object RegExp]') {\n                return string_split.call(this, separator, limit);\n            }\n\n            var output = [],\n                flags = (separator.ignoreCase ? 'i' : '') +\n                        (separator.multiline  ? 'm' : '') +\n                        (separator.extended   ? 'x' : '') + // Proposed for ES6\n                        (separator.sticky     ? 'y' : ''), // Firefox 3+\n                lastLastIndex = 0,\n                // Make `global` and avoid `lastIndex` issues by working with a copy\n                separator2, match, lastIndex, lastLength;\n            separator = new RegExp(separator.source, flags + 'g');\n            string += ''; // Type-convert\n            if (!compliantExecNpcg) {\n                // Doesn't need flags gy, but they don't hurt\n                separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n            }\n            /* Values for `limit`, per the spec:\n             * If undefined: 4294967295 // Math.pow(2, 32) - 1\n             * If 0, Infinity, or NaN: 0\n             * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n             * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n             * If other: Type-convert, then use the above rules\n             */\n            limit = limit === void 0 ?\n                -1 >>> 0 : // Math.pow(2, 32) - 1\n                ToUint32(limit);\n            while (match = separator.exec(string)) {\n                // `separator.lastIndex` is not reliable cross-browser\n                lastIndex = match.index + match[0].length;\n                if (lastIndex > lastLastIndex) {\n                    output.push(string.slice(lastLastIndex, match.index));\n                    // Fix browsers whose `exec` methods don't consistently return `undefined` for\n                    // nonparticipating capturing groups\n                    if (!compliantExecNpcg && match.length > 1) {\n                        match[0].replace(separator2, function () {\n                            for (var i = 1; i < arguments.length - 2; i++) {\n                                if (arguments[i] === void 0) {\n                                    match[i] = void 0;\n                                }\n                            }\n                        });\n                    }\n                    if (match.length > 1 && match.index < string.length) {\n                        ArrayPrototype.push.apply(output, match.slice(1));\n                    }\n                    lastLength = match[0].length;\n                    lastLastIndex = lastIndex;\n                    if (output.length >= limit) {\n                        break;\n                    }\n                }\n                if (separator.lastIndex === match.index) {\n                    separator.lastIndex++; // Avoid an infinite loop\n                }\n            }\n            if (lastLastIndex === string.length) {\n                if (lastLength || !separator.test('')) {\n                    output.push('');\n                }\n            } else {\n                output.push(string.slice(lastLastIndex));\n            }\n            return output.length > limit ? output.slice(0, limit) : output;\n        };\n    }());\n\n// [bugfix, chrome]\n// If separator is undefined, then the result array contains just one String,\n// which is the this value (converted to a String). If limit is not undefined,\n// then the output array is truncated so that it contains no more than limit\n// elements.\n// \"0\".split(undefined, 0) -> []\n} else if ('0'.split(void 0, 0).length) {\n    StringPrototype.split = function split(separator, limit) {\n        if (separator === void 0 && limit === 0) { return []; }\n        return string_split.call(this, separator, limit);\n    };\n}\n\n// ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n    substr: function substr(start, length) {\n        return string_substr.call(\n            this,\n            start < 0 ? ((start = this.length + start) < 0 ? 0 : start) : start,\n            length\n        );\n    }\n}, hasNegativeSubstrBug);\n", "'use strict';\n\n// Some extra characters that Chrome gets wrong, and substitutes with\n// something else on the wire.\n// eslint-disable-next-line no-control-regex, no-misleading-character-class\nvar extraEscapable = /[\\x00-\\x1f\\ud800-\\udfff\\ufffe\\uffff\\u0300-\\u0333\\u033d-\\u0346\\u034a-\\u034c\\u0350-\\u0352\\u0357-\\u0358\\u035c-\\u0362\\u0374\\u037e\\u0387\\u0591-\\u05af\\u05c4\\u0610-\\u0617\\u0653-\\u0654\\u0657-\\u065b\\u065d-\\u065e\\u06df-\\u06e2\\u06eb-\\u06ec\\u0730\\u0732-\\u0733\\u0735-\\u0736\\u073a\\u073d\\u073f-\\u0741\\u0743\\u0745\\u0747\\u07eb-\\u07f1\\u0951\\u0958-\\u095f\\u09dc-\\u09dd\\u09df\\u0a33\\u0a36\\u0a59-\\u0a5b\\u0a5e\\u0b5c-\\u0b5d\\u0e38-\\u0e39\\u0f43\\u0f4d\\u0f52\\u0f57\\u0f5c\\u0f69\\u0f72-\\u0f76\\u0f78\\u0f80-\\u0f83\\u0f93\\u0f9d\\u0fa2\\u0fa7\\u0fac\\u0fb9\\u1939-\\u193a\\u1a17\\u1b6b\\u1cda-\\u1cdb\\u1dc0-\\u1dcf\\u1dfc\\u1dfe\\u1f71\\u1f73\\u1f75\\u1f77\\u1f79\\u1f7b\\u1f7d\\u1fbb\\u1fbe\\u1fc9\\u1fcb\\u1fd3\\u1fdb\\u1fe3\\u1feb\\u1fee-\\u1fef\\u1ff9\\u1ffb\\u1ffd\\u2000-\\u2001\\u20d0-\\u20d1\\u20d4-\\u20d7\\u20e7-\\u20e9\\u2126\\u212a-\\u212b\\u2329-\\u232a\\u2adc\\u302b-\\u302c\\uaab2-\\uaab3\\uf900-\\ufa0d\\ufa10\\ufa12\\ufa15-\\ufa1e\\ufa20\\ufa22\\ufa25-\\ufa26\\ufa2a-\\ufa2d\\ufa30-\\ufa6d\\ufa70-\\ufad9\\ufb1d\\ufb1f\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufb4e\\ufff0-\\uffff]/g\n  , extraLookup;\n\n// This may be quite slow, so let's delay until user actually uses bad\n// characters.\nvar unrollLookup = function(escapable) {\n  var i;\n  var unrolled = {};\n  var c = [];\n  for (i = 0; i < 65536; i++) {\n    c.push( String.fromCharCode(i) );\n  }\n  escapable.lastIndex = 0;\n  c.join('').replace(escapable, function(a) {\n    unrolled[ a ] = '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n    return '';\n  });\n  escapable.lastIndex = 0;\n  return unrolled;\n};\n\n// Quote string, also taking care of unicode characters that browsers\n// often break. Especially, take care of unicode surrogates:\n// http://en.wikipedia.org/wiki/Mapping_of_Unicode_characters#Surrogates\nmodule.exports = {\n  quote: function(string) {\n    var quoted = JSON.stringify(string);\n\n    // In most cases this should be very fast and good enough.\n    extraEscapable.lastIndex = 0;\n    if (!extraEscapable.test(quoted)) {\n      return quoted;\n    }\n\n    if (!extraLookup) {\n      extraLookup = unrollLookup(extraEscapable);\n    }\n\n    return quoted.replace(extraEscapable, function(a) {\n      return extraLookup[a];\n    });\n  }\n};\n", "'use strict';\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\n\nmodule.exports = function(availableTransports) {\n  return {\n    filterToEnabled: function(transportsWhitelist, info) {\n      var transports = {\n        main: []\n      , facade: []\n      };\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n\n      availableTransports.forEach(function(trans) {\n        if (!trans) {\n          return;\n        }\n\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n\n        if (transportsWhitelist.length &&\n            transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};\n", "'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch(e) {\n    // do nothing\n  }\n\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : (level === 'log' ? function () {} : logObject.log);\n});\n\nmodule.exports = logObject;\n", "'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\n\nmodule.exports = Event;\n", "'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80'\n, protocol: 'http:'\n, host: 'localhost'\n, port: 80\n, href: 'http://localhost/'\n, hash: ''\n};\n", "'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\n\ninherits(CloseEvent, Event);\n\nmodule.exports = CloseEvent;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\n\ninherits(TransportMessageEvent, Event);\n\nmodule.exports = TransportMessageEvent;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  ;\n\nfunction XHRFake(/* method, url, payload, opts */) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.to = setTimeout(function() {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\n\ninherits(XHRFake, EventEmitter);\n\nXHRFake.prototype.close = function() {\n  clearTimeout(this.to);\n};\n\nXHRFake.timeout = 2000;\n\nmodule.exports = XHRFake;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , objectUtils = require('./utils/object')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\n\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n\n  this.xo.once('finish', function(status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = (+new Date()) - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\n\ninherits(InfoAjax, EventEmitter);\n\nInfoAjax.prototype.close = function() {\n  this.removeAllListeners();\n  this.xo.close();\n};\n\nmodule.exports = InfoAjax;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , XHRLocalObject = require('./transport/sender/xhr-local')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function(info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\n\ninherits(InfoReceiverIframe, EventEmitter);\n\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\n\nInfoReceiverIframe.prototype.close = function() {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\n\nmodule.exports = InfoReceiverIframe;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('./utils/event')\n  , IframeTransport = require('./transport/iframe')\n  , InfoReceiverIframe = require('./info-iframe-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\n\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n\n  var go = function() {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n\n    ifr.once('message', function(msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n\n        var info = d[0], rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n\n    ifr.once('close', function() {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\n\ninherits(InfoIframe, EventEmitter);\n\nInfoIframe.enabled = function() {\n  return IframeTransport.enabled();\n};\n\nInfoIframe.prototype.close = function() {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\n\nmodule.exports = InfoIframe;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , urlUtils = require('./utils/url')\n  , XDR = require('./transport/sender/xdr')\n  , XHRCors = require('./transport/sender/xhr-cors')\n  , XHRLocal = require('./transport/sender/xhr-local')\n  , XHRFake = require('./transport/sender/xhr-fake')\n  , InfoIframe = require('./info-iframe')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\n\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\n\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function(baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\n\nInfoReceiver.prototype.doXhr = function(baseUrl, urlInfo) {\n  var self = this\n    , url = urlUtils.addPath(baseUrl, '/info')\n    ;\n  debug('doXhr', url);\n\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n\n  this.timeoutRef = setTimeout(function() {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n\n  this.xo.once('finish', function(info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\n\nInfoReceiver.prototype._cleanup = function(wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\n\nInfoReceiver.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\n\nInfoReceiver.timeout = 8000;\n\nmodule.exports = InfoReceiver;\n", "'use strict';\n\nvar iframeUtils = require('./utils/iframe')\n  ;\n\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\n\nFacadeJS.prototype._transportClose = function(code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function(frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function(data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function() {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\n\nmodule.exports = FacadeJS;\n", "'use strict';\n\nvar urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , FacadeJS = require('./facade')\n  , InfoIframeReceiver = require('./info-iframe-receiver')\n  , iframeUtils = require('./utils/iframe')\n  , loc = require('./location')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\n\nmodule.exports = function(SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function(at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  });\n\n  // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n\n  /* eslint-disable camelcase */\n  SockJS.bootstrap_iframe = function() {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n    var onMessage = function(e) {\n      if (e.source !== parent) {\n        return;\n      }\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n\n      var iframeMessage;\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n      switch (iframeMessage.type) {\n      case 's':\n        var p;\n        try {\n          p = JSON.parse(iframeMessage.data);\n        } catch (ignored) {\n          debug('bad json', iframeMessage.data);\n          break;\n        }\n        var version = p[0];\n        var transport = p[1];\n        var transUrl = p[2];\n        var baseUrl = p[3];\n        debug(version, transport, transUrl, baseUrl);\n        // change this to semver logic\n        if (version !== SockJS.version) {\n          throw new Error('Incompatible SockJS! Main site uses:' +\n                    ' \"' + version + '\", the iframe:' +\n                    ' \"' + SockJS.version + '\".');\n        }\n\n        if (!urlUtils.isOriginEqual(transUrl, loc.href) ||\n            !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n          throw new Error('Can\\'t connect to different domain from within an ' +\n                    'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n        }\n        facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n        break;\n      case 'm':\n        facade._send(iframeMessage.data);\n        break;\n      case 'c':\n        if (facade) {\n          facade._close();\n        }\n        facade = null;\n        break;\n      }\n    };\n\n    eventUtils.attachEvent('message', onMessage);\n\n    // Start\n    iframeUtils.postMessage('s');\n  };\n};\n", "'use strict';\n\nrequire('./shims');\n\nvar URL = require('url-parse')\n  , inherits = require('inherits')\n  , random = require('./utils/random')\n  , escape = require('./utils/escape')\n  , urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , transport = require('./utils/transport')\n  , objectUtils = require('./utils/object')\n  , browser = require('./utils/browser')\n  , log = require('./utils/log')\n  , Event = require('./event/event')\n  , EventTarget = require('./event/eventtarget')\n  , loc = require('./location')\n  , CloseEvent = require('./event/close')\n  , TransportMessageEvent = require('./event/trans-message')\n  , InfoReceiver = require('./info-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\n\nvar transports;\n\n// follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n  EventTarget.call(this);\n\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = '';\n\n  // non-standard extension\n  options = options || {};\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n\n  var sessionId = options.sessionId || 8;\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function() {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n\n  this._server = options.server || random.numberString(1000);\n\n  // Step 1 of WS spec - parse and validate the url. Issue #8\n  var parsedUrl = new URL(url);\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n\n  var secure = parsedUrl.protocol === 'https:';\n  // Step 2 - don't allow secure origin with an insecure protocol\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  }\n\n  // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  }\n\n  // Step 5 - check protocols argument\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function(proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n    if (i < (sortedProtocols.length - 1) && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  });\n\n  // Step 6 - convert origin\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null;\n\n  // remove the trailing slash\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, ''));\n\n  // store the sanitized url\n  this.url = parsedUrl.href;\n  debug('using url', this.url);\n\n  // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain()\n  , sameOrigin: urlUtils.isOriginEqual(this.url, loc.href)\n  , sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\n\ninherits(SockJS, EventTarget);\n\nfunction userSetCode(code) {\n  return code === 1000 || (code >= 3000 && code <= 4999);\n}\n\nSockJS.prototype.close = function(code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  }\n  // Step 2.4 states the max is 123 bytes, but we are just checking length\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  }\n\n  // Step 3.1\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  }\n\n  // TODO look at docs to determine how to set this\n  var wasClean = true;\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\n\nSockJS.prototype.send = function(data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n  this._transport.send(escape.quote(data));\n};\n\nSockJS.version = require('./version');\n\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\n\nSockJS.prototype._receiveInfo = function(info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n    return;\n  }\n\n  // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n  this._rto = this.countRTO(rtt);\n  // allow server to override url used for the actual transport\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info);\n  // determine list of desired and supported transports\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n\n  this._connect();\n};\n\nSockJS.prototype._connect = function() {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n    if (Transport.needBody) {\n      if (!global.document.body ||\n          (typeof global.document.readyState !== 'undefined' &&\n            global.document.readyState !== 'complete' &&\n            global.document.readyState !== 'interactive')) {\n        debug('waiting for body');\n        this._transports.unshift(Transport);\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    }\n\n    // calculate timeout based on RTO and round trips. Default to 5s\n    var timeoutMs = Math.max(this._timeout, (this._rto * Transport.roundTrips) || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n\n    return;\n  }\n  this._close(2000, 'All transports failed', false);\n};\n\nSockJS.prototype._transportTimeout = function() {\n  debug('_transportTimeout');\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\n\nSockJS.prototype._transportMessage = function(msg) {\n  debug('_transportMessage', msg);\n  var self = this\n    , type = msg.slice(0, 1)\n    , content = msg.slice(1)\n    , payload\n    ;\n\n  // first check for messages that don't need a payload\n  switch (type) {\n    case 'o':\n      this._open();\n      return;\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function(p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n      break;\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n      break;\n  }\n};\n\nSockJS.prototype._transportClose = function(code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n  if (this._transport) {\n    this._transport.removeAllListeners();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n    return;\n  }\n\n  this._close(code, reason);\n};\n\nSockJS.prototype._open = function() {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\n\nSockJS.prototype._close = function(code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n\n  if (this._ir) {\n    forceFail = true;\n    this._ir.close();\n    this._ir = null;\n  }\n  if (this._transport) {\n    this._transport.close();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function() {\n    this.readyState = SockJS.CLOSED;\n\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n};\n\n// See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\nSockJS.prototype.countRTO = function(rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\n\nmodule.exports = function(availableTransports) {\n  transports = transport(availableTransports);\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n  return SockJS;\n};\n", "'use strict';\n\nvar transportList = require('./transport-list');\n\nmodule.exports = require('./main')(transportList);\n\n// TODO can't get rid of this until all servers do\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAI,OAAO,UAAU,OAAO,OAAO,iBAAiB;AAClD,aAAO,QAAQ,cAAc,SAAS,QAAQ;AAC5C,YAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,eAAO,OAAO,gBAAgB,KAAK;AACnC,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,aAAO,QAAQ,cAAc,SAAS,QAAQ;AAC5C,YAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAM,CAAC,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,QAC3C;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI,SAAS;AAIb,QAAI,qBAAqB;AACzB,WAAO,UAAU;AAAA,MACf,QAAQ,SAAS,QAAQ;AACvB,YAAI,MAAM,mBAAmB;AAC7B,YAAI,QAAQ,OAAO,YAAY,MAAM;AACrC,YAAI,MAAM,CAAC;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,mBAAmB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;AAAA,QACvD;AACA,eAAO,IAAI,KAAK,EAAE;AAAA,MACpB;AAAA,MAEA,QAAQ,SAAS,KAAK;AACpB,eAAO,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG;AAAA,MACvC;AAAA,MAEA,cAAc,SAAS,KAAK;AAC1B,YAAI,KAAK,MAAM,MAAM,IAAI;AACzB,YAAI,IAAI,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,GAAG;AACjC,gBAAQ,IAAI,KAAK,OAAO,GAAG,GAAG,MAAM,CAAC,CAAC;AAAA,MACxC;AAAA,IACF;AAAA;AAAA;;;AC3BA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,QAAI,WAAW,CAAC;AAAhB,QACI,cAAc;AADlB,QAGI,sBAAsB,OAAO,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,IAAI;AAGlF,WAAO,UAAU;AAAA,MACf,aAAa,SAAS,OAAO,UAAU;AACrC,YAAI,OAAO,OAAO,qBAAqB,aAAa;AAClD,iBAAO,iBAAiB,OAAO,UAAU,KAAK;AAAA,QAChD,WAAW,OAAO,YAAY,OAAO,aAAa;AAIhD,iBAAO,SAAS,YAAY,OAAO,OAAO,QAAQ;AAElD,iBAAO,YAAY,OAAO,OAAO,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,MAEA,aAAa,SAAS,OAAO,UAAU;AACrC,YAAI,OAAO,OAAO,qBAAqB,aAAa;AAClD,iBAAO,oBAAoB,OAAO,UAAU,KAAK;AAAA,QACnD,WAAW,OAAO,YAAY,OAAO,aAAa;AAChD,iBAAO,SAAS,YAAY,OAAO,OAAO,QAAQ;AAClD,iBAAO,YAAY,OAAO,OAAO,QAAQ;AAAA,QAC3C;AAAA,MACF;AAAA,MAEA,WAAW,SAAS,UAAU;AAC5B,YAAI,qBAAqB;AACvB,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,OAAO,OAAO,CAAC;AACzB,iBAAS,GAAG,IAAI;AAChB,YAAI,aAAa;AACf,qBAAW,KAAK,wBAAwB,CAAC;AAAA,QAC3C;AACA,eAAO;AAAA,MACT;AAAA,MAEA,WAAW,SAAS,KAAK;AACvB,YAAI,OAAO,UAAU;AACnB,iBAAO,SAAS,GAAG;AAAA,QACrB;AAAA,MACF;AAAA,MAEA,wBAAwB,WAAW;AACjC,iBAAS,OAAO,UAAU;AACxB,mBAAS,GAAG,EAAE;AACd,iBAAO,SAAS,GAAG;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,kBAAkB,WAAW;AAC/B,UAAI,aAAa;AACf;AAAA,MACF;AACA,oBAAc;AACd,aAAO,QAAQ,uBAAuB;AAAA,IACxC;AAIA,QAAI,CAAC,qBAAqB;AACxB,aAAO,QAAQ,YAAY,UAAU,eAAe;AAAA,IACtD;AAAA;AAAA;;;ACxEA;AAAA;AAAA;AAWA,WAAO,UAAU,SAAS,SAAS,MAAM,UAAU;AACjD,iBAAW,SAAS,MAAM,GAAG,EAAE,CAAC;AAChC,aAAO,CAAC;AAER,UAAI,CAAC;AAAM,eAAO;AAElB,cAAQ,UAAU;AAAA,QAChB,KAAK;AAAA,QACL,KAAK;AACL,iBAAO,SAAS;AAAA,QAEhB,KAAK;AAAA,QACL,KAAK;AACL,iBAAO,SAAS;AAAA,QAEhB,KAAK;AACL,iBAAO,SAAS;AAAA,QAEhB,KAAK;AACL,iBAAO,SAAS;AAAA,QAEhB,KAAK;AACL,iBAAO;AAAA,MACT;AAEA,aAAO,SAAS;AAAA,IAClB;AAAA;AAAA;;;ACrCA;AAAA;AAAA;AAEA,QAAI,MAAM,OAAO,UAAU;AAA3B,QACI;AASJ,aAAS,OAAO,OAAO;AACrB,UAAI;AACF,eAAO,mBAAmB,MAAM,QAAQ,OAAO,GAAG,CAAC;AAAA,MACrD,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AASA,aAAS,OAAO,OAAO;AACrB,UAAI;AACF,eAAO,mBAAmB,KAAK;AAAA,MACjC,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AASA,aAAS,YAAY,OAAO;AAC1B,UAAI,SAAS,wBACT,SAAS,CAAC,GACV;AAEJ,aAAO,OAAO,OAAO,KAAK,KAAK,GAAG;AAChC,YAAI,MAAM,OAAO,KAAK,CAAC,CAAC,GACpB,QAAQ,OAAO,KAAK,CAAC,CAAC;AAU1B,YAAI,QAAQ,QAAQ,UAAU,QAAQ,OAAO;AAAQ;AACrD,eAAO,GAAG,IAAI;AAAA,MAChB;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,eAAe,KAAK,QAAQ;AACnC,eAAS,UAAU;AAEnB,UAAI,QAAQ,CAAC,GACT,OACA;AAKJ,UAAI,aAAa,OAAO;AAAQ,iBAAS;AAEzC,WAAK,OAAO,KAAK;AACf,YAAI,IAAI,KAAK,KAAK,GAAG,GAAG;AACtB,kBAAQ,IAAI,GAAG;AAMf,cAAI,CAAC,UAAU,UAAU,QAAQ,UAAU,SAAS,MAAM,KAAK,IAAI;AACjE,oBAAQ;AAAA,UACV;AAEA,gBAAM,OAAO,GAAG;AAChB,kBAAQ,OAAO,KAAK;AAMpB,cAAI,QAAQ,QAAQ,UAAU;AAAM;AACpC,gBAAM,KAAK,MAAK,MAAK,KAAK;AAAA,QAC5B;AAAA,MACF;AAEA,aAAO,MAAM,SAAS,SAAS,MAAM,KAAK,GAAG,IAAI;AAAA,IACnD;AAKA,YAAQ,YAAY;AACpB,YAAQ,QAAQ;AAAA;AAAA;;;ACrHhB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,KAAK;AADT,QAEI,sBAAsB;AAF1B,QAGI,SAAS;AAHb,QAII,UAAU;AAJd,QAKI,OAAO;AALX,QAMI,aAAa;AANjB,QAOI,qBAAqB;AAUzB,aAAS,SAAS,KAAK;AACrB,cAAQ,MAAM,MAAM,IAAI,SAAS,EAAE,QAAQ,qBAAqB,EAAE;AAAA,IACpE;AAcA,QAAI,QAAQ;AAAA,MACV,CAAC,KAAK,MAAM;AAAA;AAAA,MACZ,CAAC,KAAK,OAAO;AAAA;AAAA,MACb,SAAS,SAAS,SAAS,KAAK;AAC9B,eAAO,UAAU,IAAI,QAAQ,IAAI,QAAQ,QAAQ,OAAO,GAAG,IAAI;AAAA,MACjE;AAAA,MACA,CAAC,KAAK,UAAU;AAAA;AAAA,MAChB,CAAC,KAAK,QAAQ,CAAC;AAAA;AAAA,MACf,CAAC,KAAK,QAAQ,QAAW,GAAG,CAAC;AAAA;AAAA,MAC7B,CAAC,WAAW,QAAQ,QAAW,CAAC;AAAA;AAAA,MAChC,CAAC,KAAK,YAAY,QAAW,GAAG,CAAC;AAAA;AAAA,IACnC;AAUA,QAAI,SAAS,EAAE,MAAM,GAAG,OAAO,EAAE;AAcjC,aAAS,UAAU,KAAK;AACtB,UAAI;AAEJ,UAAI,OAAO,WAAW;AAAa,oBAAY;AAAA,eACtC,OAAO,WAAW;AAAa,oBAAY;AAAA,eAC3C,OAAO,SAAS;AAAa,oBAAY;AAAA;AAC7C,oBAAY,CAAC;AAElB,UAAI,WAAW,UAAU,YAAY,CAAC;AACtC,YAAM,OAAO;AAEb,UAAI,mBAAmB,CAAC,GACpB,OAAO,OAAO,KACd;AAEJ,UAAI,YAAY,IAAI,UAAU;AAC5B,2BAAmB,IAAI,IAAI,SAAS,IAAI,QAAQ,GAAG,CAAC,CAAC;AAAA,MACvD,WAAW,aAAa,MAAM;AAC5B,2BAAmB,IAAI,IAAI,KAAK,CAAC,CAAC;AAClC,aAAK,OAAO;AAAQ,iBAAO,iBAAiB,GAAG;AAAA,MACjD,WAAW,aAAa,MAAM;AAC5B,aAAK,OAAO,KAAK;AACf,cAAI,OAAO;AAAQ;AACnB,2BAAiB,GAAG,IAAI,IAAI,GAAG;AAAA,QACjC;AAEA,YAAI,iBAAiB,YAAY,QAAW;AAC1C,2BAAiB,UAAU,QAAQ,KAAK,IAAI,IAAI;AAAA,QAClD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AASA,aAAS,UAAU,QAAQ;AACzB,aACE,WAAW,WACX,WAAW,UACX,WAAW,WACX,WAAW,YACX,WAAW,SACX,WAAW;AAAA,IAEf;AAkBA,aAAS,gBAAgB,SAAS,UAAU;AAC1C,gBAAU,SAAS,OAAO;AAC1B,gBAAU,QAAQ,QAAQ,QAAQ,EAAE;AACpC,iBAAW,YAAY,CAAC;AAExB,UAAI,QAAQ,WAAW,KAAK,OAAO;AACnC,UAAI,WAAW,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,YAAY,IAAI;AACnD,UAAI,iBAAiB,CAAC,CAAC,MAAM,CAAC;AAC9B,UAAI,eAAe,CAAC,CAAC,MAAM,CAAC;AAC5B,UAAI,eAAe;AACnB,UAAI;AAEJ,UAAI,gBAAgB;AAClB,YAAI,cAAc;AAChB,iBAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC;AACpC,yBAAe,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE;AAAA,QAC5C,OAAO;AACL,iBAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AACzB,yBAAe,MAAM,CAAC,EAAE;AAAA,QAC1B;AAAA,MACF,OAAO;AACL,YAAI,cAAc;AAChB,iBAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AACzB,yBAAe,MAAM,CAAC,EAAE;AAAA,QAC1B,OAAO;AACL,iBAAO,MAAM,CAAC;AAAA,QAChB;AAAA,MACF;AAEA,UAAI,aAAa,SAAS;AACxB,YAAI,gBAAgB,GAAG;AACrB,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB;AAAA,MACF,WAAW,UAAU,QAAQ,GAAG;AAC9B,eAAO,MAAM,CAAC;AAAA,MAChB,WAAW,UAAU;AACnB,YAAI,gBAAgB;AAClB,iBAAO,KAAK,MAAM,CAAC;AAAA,QACrB;AAAA,MACF,WAAW,gBAAgB,KAAK,UAAU,SAAS,QAAQ,GAAG;AAC5D,eAAO,MAAM,CAAC;AAAA,MAChB;AAEA,aAAO;AAAA,QACL;AAAA,QACA,SAAS,kBAAkB,UAAU,QAAQ;AAAA,QAC7C;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAUA,aAAS,QAAQ,UAAU,MAAM;AAC/B,UAAI,aAAa;AAAI,eAAO;AAE5B,UAAI,QAAQ,QAAQ,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,OAAO,SAAS,MAAM,GAAG,CAAC,GACvE,IAAI,KAAK,QACT,OAAO,KAAK,IAAI,CAAC,GACjB,UAAU,OACV,KAAK;AAET,aAAO,KAAK;AACV,YAAI,KAAK,CAAC,MAAM,KAAK;AACnB,eAAK,OAAO,GAAG,CAAC;AAAA,QAClB,WAAW,KAAK,CAAC,MAAM,MAAM;AAC3B,eAAK,OAAO,GAAG,CAAC;AAChB;AAAA,QACF,WAAW,IAAI;AACb,cAAI,MAAM;AAAG,sBAAU;AACvB,eAAK,OAAO,GAAG,CAAC;AAChB;AAAA,QACF;AAAA,MACF;AAEA,UAAI;AAAS,aAAK,QAAQ,EAAE;AAC5B,UAAI,SAAS,OAAO,SAAS;AAAM,aAAK,KAAK,EAAE;AAE/C,aAAO,KAAK,KAAK,GAAG;AAAA,IACtB;AAgBA,aAAS,IAAI,SAAS,UAAU,QAAQ;AACtC,gBAAU,SAAS,OAAO;AAC1B,gBAAU,QAAQ,QAAQ,QAAQ,EAAE;AAEpC,UAAI,EAAE,gBAAgB,MAAM;AAC1B,eAAO,IAAI,IAAI,SAAS,UAAU,MAAM;AAAA,MAC1C;AAEA,UAAI,UAAU,WAAW,OAAO,aAAa,OAAO,KAChD,eAAe,MAAM,MAAM,GAC3B,OAAO,OAAO,UACd,MAAM,MACN,IAAI;AAaR,UAAI,aAAa,QAAQ,aAAa,MAAM;AAC1C,iBAAS;AACT,mBAAW;AAAA,MACb;AAEA,UAAI,UAAU,eAAe,OAAO;AAAQ,iBAAS,GAAG;AAExD,iBAAW,UAAU,QAAQ;AAK7B,kBAAY,gBAAgB,WAAW,IAAI,QAAQ;AACnD,iBAAW,CAAC,UAAU,YAAY,CAAC,UAAU;AAC7C,UAAI,UAAU,UAAU,WAAW,YAAY,SAAS;AACxD,UAAI,WAAW,UAAU,YAAY,SAAS,YAAY;AAC1D,gBAAU,UAAU;AAMpB,UACE,UAAU,aAAa,YACrB,UAAU,iBAAiB,KAAK,mBAAmB,KAAK,OAAO,MAChE,CAAC,UAAU,YACT,UAAU,YACT,UAAU,eAAe,KACzB,CAAC,UAAU,IAAI,QAAQ,IAC3B;AACA,qBAAa,CAAC,IAAI,CAAC,QAAQ,UAAU;AAAA,MACvC;AAEA,aAAO,IAAI,aAAa,QAAQ,KAAK;AACnC,sBAAc,aAAa,CAAC;AAE5B,YAAI,OAAO,gBAAgB,YAAY;AACrC,oBAAU,YAAY,SAAS,GAAG;AAClC;AAAA,QACF;AAEA,gBAAQ,YAAY,CAAC;AACrB,cAAM,YAAY,CAAC;AAEnB,YAAI,UAAU,OAAO;AACnB,cAAI,GAAG,IAAI;AAAA,QACb,WAAW,aAAa,OAAO,OAAO;AACpC,kBAAQ,UAAU,MACd,QAAQ,YAAY,KAAK,IACzB,QAAQ,QAAQ,KAAK;AAEzB,cAAI,CAAC,OAAO;AACV,gBAAI,aAAa,OAAO,YAAY,CAAC,GAAG;AACtC,kBAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,KAAK;AACjC,wBAAU,QAAQ,MAAM,QAAQ,YAAY,CAAC,CAAC;AAAA,YAChD,OAAO;AACL,kBAAI,GAAG,IAAI,QAAQ,MAAM,KAAK;AAC9B,wBAAU,QAAQ,MAAM,GAAG,KAAK;AAAA,YAClC;AAAA,UACF;AAAA,QACF,WAAY,QAAQ,MAAM,KAAK,OAAO,GAAI;AACxC,cAAI,GAAG,IAAI,MAAM,CAAC;AAClB,oBAAU,QAAQ,MAAM,GAAG,MAAM,KAAK;AAAA,QACxC;AAEA,YAAI,GAAG,IAAI,IAAI,GAAG,MAChB,YAAY,YAAY,CAAC,IAAI,SAAS,GAAG,KAAK,KAAK;AAOrD,YAAI,YAAY,CAAC;AAAG,cAAI,GAAG,IAAI,IAAI,GAAG,EAAE,YAAY;AAAA,MACtD;AAOA,UAAI;AAAQ,YAAI,QAAQ,OAAO,IAAI,KAAK;AAKxC,UACI,YACC,SAAS,WACT,IAAI,SAAS,OAAO,CAAC,MAAM,QAC1B,IAAI,aAAa,MAAM,SAAS,aAAa,KACjD;AACA,YAAI,WAAW,QAAQ,IAAI,UAAU,SAAS,QAAQ;AAAA,MACxD;AAMA,UAAI,IAAI,SAAS,OAAO,CAAC,MAAM,OAAO,UAAU,IAAI,QAAQ,GAAG;AAC7D,YAAI,WAAW,MAAM,IAAI;AAAA,MAC3B;AAOA,UAAI,CAAC,SAAS,IAAI,MAAM,IAAI,QAAQ,GAAG;AACrC,YAAI,OAAO,IAAI;AACf,YAAI,OAAO;AAAA,MACb;AAKA,UAAI,WAAW,IAAI,WAAW;AAE9B,UAAI,IAAI,MAAM;AACZ,gBAAQ,IAAI,KAAK,QAAQ,GAAG;AAE5B,YAAI,CAAC,OAAO;AACV,cAAI,WAAW,IAAI,KAAK,MAAM,GAAG,KAAK;AACtC,cAAI,WAAW,mBAAmB,mBAAmB,IAAI,QAAQ,CAAC;AAElE,cAAI,WAAW,IAAI,KAAK,MAAM,QAAQ,CAAC;AACvC,cAAI,WAAW,mBAAmB,mBAAmB,IAAI,QAAQ,CAAC;AAAA,QACpE,OAAO;AACL,cAAI,WAAW,mBAAmB,mBAAmB,IAAI,IAAI,CAAC;AAAA,QAChE;AAEA,YAAI,OAAO,IAAI,WAAW,IAAI,WAAU,MAAK,IAAI,WAAW,IAAI;AAAA,MAClE;AAEA,UAAI,SAAS,IAAI,aAAa,WAAW,UAAU,IAAI,QAAQ,KAAK,IAAI,OACpE,IAAI,WAAU,OAAM,IAAI,OACxB;AAKJ,UAAI,OAAO,IAAI,SAAS;AAAA,IAC1B;AAeA,aAAS,IAAI,MAAM,OAAO,IAAI;AAC5B,UAAI,MAAM;AAEV,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,cAAI,aAAa,OAAO,SAAS,MAAM,QAAQ;AAC7C,qBAAS,MAAM,GAAG,OAAO,KAAK;AAAA,UAChC;AAEA,cAAI,IAAI,IAAI;AACZ;AAAA,QAEF,KAAK;AACH,cAAI,IAAI,IAAI;AAEZ,cAAI,CAAC,SAAS,OAAO,IAAI,QAAQ,GAAG;AAClC,gBAAI,OAAO,IAAI;AACf,gBAAI,IAAI,IAAI;AAAA,UACd,WAAW,OAAO;AAChB,gBAAI,OAAO,IAAI,WAAU,MAAK;AAAA,UAChC;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,IAAI,IAAI;AAEZ,cAAI,IAAI;AAAM,qBAAS,MAAK,IAAI;AAChC,cAAI,OAAO;AACX;AAAA,QAEF,KAAK;AACH,cAAI,IAAI,IAAI;AAEZ,cAAI,KAAK,KAAK,KAAK,GAAG;AACpB,oBAAQ,MAAM,MAAM,GAAG;AACvB,gBAAI,OAAO,MAAM,IAAI;AACrB,gBAAI,WAAW,MAAM,KAAK,GAAG;AAAA,UAC/B,OAAO;AACL,gBAAI,WAAW;AACf,gBAAI,OAAO;AAAA,UACb;AAEA;AAAA,QAEF,KAAK;AACH,cAAI,WAAW,MAAM,YAAY;AACjC,cAAI,UAAU,CAAC;AACf;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO;AACT,gBAAI,OAAO,SAAS,aAAa,MAAM;AACvC,gBAAI,IAAI,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,OAAO,QAAQ;AAAA,UACxD,OAAO;AACL,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,IAAI,IAAI,mBAAmB,KAAK;AACpC;AAAA,QAEF,KAAK;AACH,cAAI,QAAQ,MAAM,QAAQ,GAAG;AAE7B,cAAI,CAAC,OAAO;AACV,gBAAI,WAAW,MAAM,MAAM,GAAG,KAAK;AACnC,gBAAI,WAAW,mBAAmB,mBAAmB,IAAI,QAAQ,CAAC;AAElE,gBAAI,WAAW,MAAM,MAAM,QAAQ,CAAC;AACpC,gBAAI,WAAW,mBAAmB,mBAAmB,IAAI,QAAQ,CAAC;AAAA,UACpE,OAAO;AACL,gBAAI,WAAW,mBAAmB,mBAAmB,KAAK,CAAC;AAAA,UAC7D;AAAA,MACJ;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,MAAM,MAAM,CAAC;AAEjB,YAAI,IAAI,CAAC;AAAG,cAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,YAAY;AAAA,MACpD;AAEA,UAAI,OAAO,IAAI,WAAW,IAAI,WAAU,MAAK,IAAI,WAAW,IAAI;AAEhE,UAAI,SAAS,IAAI,aAAa,WAAW,UAAU,IAAI,QAAQ,KAAK,IAAI,OACpE,IAAI,WAAU,OAAM,IAAI,OACxB;AAEJ,UAAI,OAAO,IAAI,SAAS;AAExB,aAAO;AAAA,IACT;AASA,aAAS,SAAS,WAAW;AAC3B,UAAI,CAAC,aAAa,eAAe,OAAO;AAAW,oBAAY,GAAG;AAElE,UAAI,OACA,MAAM,MACN,OAAO,IAAI,MACX,WAAW,IAAI;AAEnB,UAAI,YAAY,SAAS,OAAO,SAAS,SAAS,CAAC,MAAM;AAAK,oBAAY;AAE1E,UAAI,SACF,YACE,IAAI,YAAY,IAAI,WAAY,UAAU,IAAI,QAAQ,IAAI,OAAO;AAErE,UAAI,IAAI,UAAU;AAChB,kBAAU,IAAI;AACd,YAAI,IAAI;AAAU,oBAAU,MAAK,IAAI;AACrC,kBAAU;AAAA,MACZ,WAAW,IAAI,UAAU;AACvB,kBAAU,MAAK,IAAI;AACnB,kBAAU;AAAA,MACZ,WACE,IAAI,aAAa,WACjB,UAAU,IAAI,QAAQ,KACtB,CAAC,QACD,IAAI,aAAa,KACjB;AAKA,kBAAU;AAAA,MACZ;AAOA,UAAI,KAAK,KAAK,SAAS,CAAC,MAAM,OAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,MAAO;AAC3E,gBAAQ;AAAA,MACV;AAEA,gBAAU,OAAO,IAAI;AAErB,cAAQ,aAAa,OAAO,IAAI,QAAQ,UAAU,IAAI,KAAK,IAAI,IAAI;AACnE,UAAI;AAAO,kBAAU,QAAQ,MAAM,OAAO,CAAC,IAAI,MAAK,QAAQ;AAE5D,UAAI,IAAI;AAAM,kBAAU,IAAI;AAE5B,aAAO;AAAA,IACT;AAEA,QAAI,YAAY,EAAE,KAAU,SAAmB;AAM/C,QAAI,kBAAkB;AACtB,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,KAAK;AAET,WAAO,UAAU;AAAA;AAAA;;;AC5kBjB;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAU,KAAK,SAAS;AACvC,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAO,MAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,SAAS,GAAG,GAAG;AAC7C,eAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAAS,MAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,mIAAmI;AAAA,QAC7I;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAUA,aAAS,QAAQ,IAAI;AACnB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,KAAK;AAAA,MACnC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO,KAAK;AAAA,IACd;AAMA,aAAS,OAAO,IAAI,OAAO,GAAG,MAAM;AAClC,UAAI,WAAW,SAAS,IAAI;AAC5B,aAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,QAAQ,WAAW,MAAM;AAAA,IAC7D;AAAA;AAAA;;;ACjKA;AAAA;AAAA;AAMA,aAAS,MAAM,KAAK;AAClB,kBAAY,QAAQ;AACpB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,SAAS;AACrB,kBAAY,UAAU;AACtB,kBAAY,WAAW;AACvB,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAU,KAAK;AACtC,oBAAY,GAAG,IAAI,IAAI,GAAG;AAAA,MAC5B,CAAC;AAKD,kBAAY,YAAY,CAAC;AAKzB,kBAAY,QAAQ,CAAC;AACrB,kBAAY,QAAQ,CAAC;AAOrB,kBAAY,aAAa,CAAC;AAQ1B,eAAS,YAAY,WAAW;AAC9B,YAAI,OAAO;AAEX,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAQ,QAAQ,KAAK,OAAO,UAAU,WAAW,CAAC;AAClD,kBAAQ;AAAA,QACV;AAEA,eAAO,YAAY,OAAO,KAAK,IAAI,IAAI,IAAI,YAAY,OAAO,MAAM;AAAA,MACtE;AAEA,kBAAY,cAAc;AAS1B,eAAS,YAAY,WAAW;AAC9B,YAAI;AAEJ,iBAAS,QAAQ;AAEf,cAAI,CAAC,MAAM,SAAS;AAClB;AAAA,UACF;AAEA,mBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,iBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,UAC7B;AAEA,cAAIA,QAAO;AAEX,cAAI,OAAO,OAAO,oBAAI,KAAK,CAAC;AAC5B,cAAI,KAAK,QAAQ,YAAY;AAC7B,UAAAA,MAAK,OAAO;AACZ,UAAAA,MAAK,OAAO;AACZ,UAAAA,MAAK,OAAO;AACZ,qBAAW;AACX,eAAK,CAAC,IAAI,YAAY,OAAO,KAAK,CAAC,CAAC;AAEpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAE/B,iBAAK,QAAQ,IAAI;AAAA,UACnB;AAGA,cAAI,QAAQ;AACZ,eAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,SAAU,OAAO,QAAQ;AAElE,gBAAI,UAAU,MAAM;AAClB,qBAAO;AAAA,YACT;AAEA;AACA,gBAAI,YAAY,YAAY,WAAW,MAAM;AAE7C,gBAAI,OAAO,cAAc,YAAY;AACnC,kBAAI,MAAM,KAAK,KAAK;AACpB,sBAAQ,UAAU,KAAKA,OAAM,GAAG;AAEhC,mBAAK,OAAO,OAAO,CAAC;AACpB;AAAA,YACF;AAEA,mBAAO;AAAA,UACT,CAAC;AAED,sBAAY,WAAW,KAAKA,OAAM,IAAI;AACtC,cAAI,QAAQA,MAAK,OAAO,YAAY;AACpC,gBAAM,MAAMA,OAAM,IAAI;AAAA,QACxB;AAEA,cAAM,YAAY;AAClB,cAAM,UAAU,YAAY,QAAQ,SAAS;AAC7C,cAAM,YAAY,YAAY,UAAU;AACxC,cAAM,QAAQ,YAAY,SAAS;AACnC,cAAM,UAAU;AAChB,cAAM,SAAS;AAIf,YAAI,OAAO,YAAY,SAAS,YAAY;AAC1C,sBAAY,KAAK,KAAK;AAAA,QACxB;AAEA,oBAAY,UAAU,KAAK,KAAK;AAChC,eAAO;AAAA,MACT;AAEA,eAAS,UAAU;AACjB,YAAI,QAAQ,YAAY,UAAU,QAAQ,IAAI;AAE9C,YAAI,UAAU,IAAI;AAChB,sBAAY,UAAU,OAAO,OAAO,CAAC;AACrC,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO,WAAW,WAAW;AACpC,eAAO,YAAY,KAAK,aAAa,OAAO,cAAc,cAAc,MAAM,aAAa,SAAS;AAAA,MACtG;AAUA,eAAS,OAAO,YAAY;AAC1B,oBAAY,KAAK,UAAU;AAC3B,oBAAY,QAAQ,CAAC;AACrB,oBAAY,QAAQ,CAAC;AACrB,YAAI;AACJ,YAAI,SAAS,OAAO,eAAe,WAAW,aAAa,IAAI,MAAM,QAAQ;AAC7E,YAAI,MAAM,MAAM;AAEhB,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,cAAI,CAAC,MAAM,CAAC,GAAG;AAEb;AAAA,UACF;AAEA,uBAAa,MAAM,CAAC,EAAE,QAAQ,OAAO,KAAK;AAE1C,cAAI,WAAW,CAAC,MAAM,KAAK;AACzB,wBAAY,MAAM,KAAK,IAAI,OAAO,MAAM,WAAW,OAAO,CAAC,IAAI,GAAG,CAAC;AAAA,UACrE,OAAO;AACL,wBAAY,MAAM,KAAK,IAAI,OAAO,MAAM,aAAa,GAAG,CAAC;AAAA,UAC3D;AAAA,QACF;AAEA,aAAK,IAAI,GAAG,IAAI,YAAY,UAAU,QAAQ,KAAK;AACjD,cAAI,WAAW,YAAY,UAAU,CAAC;AACtC,mBAAS,UAAU,YAAY,QAAQ,SAAS,SAAS;AAAA,QAC3D;AAAA,MACF;AAQA,eAAS,UAAU;AACjB,oBAAY,OAAO,EAAE;AAAA,MACvB;AAUA,eAAS,QAAQ,MAAM;AACrB,YAAI,KAAK,KAAK,SAAS,CAAC,MAAM,KAAK;AACjC,iBAAO;AAAA,QACT;AAEA,YAAI;AACJ,YAAI;AAEJ,aAAK,IAAI,GAAG,MAAM,YAAY,MAAM,QAAQ,IAAI,KAAK,KAAK;AACxD,cAAI,YAAY,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AACnC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,aAAK,IAAI,GAAG,MAAM,YAAY,MAAM,QAAQ,IAAI,KAAK,KAAK;AACxD,cAAI,YAAY,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AACnC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAUA,eAAS,OAAO,KAAK;AACnB,YAAI,eAAe,OAAO;AACxB,iBAAO,IAAI,SAAS,IAAI;AAAA,QAC1B;AAEA,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,YAAY,KAAK,CAAC;AACrC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvPjB;AAAA;AAAA;AAEA,aAAS,QAAQ,KAAK;AAAE,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,kBAAU,SAASC,SAAQC,MAAK;AAAE,iBAAO,OAAOA;AAAA,QAAK;AAAA,MAAG,OAAO;AAAE,kBAAU,SAASD,SAAQC,MAAK;AAAE,iBAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,QAAK;AAAA,MAAG;AAAE,aAAO,QAAQ,GAAG;AAAA,IAAG;AAO9V,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,aAAa;AAK/B,YAAQ,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAUp1B,aAAS,YAAY;AAInB,UAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,SAAS,cAAc,OAAO,QAAQ,SAAS;AACpH,eAAO;AAAA,MACT;AAGA,UAAI,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,GAAG;AAC/H,eAAO;AAAA,MACT;AAIA,aAAO,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MACvI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAW,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAEzH,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,KAAK,SAAS,OAAO,IAAI,EAAE,KAAK;AAAA,MACnJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IACzH;AAQA,aAAS,WAAW,MAAM;AACxB,WAAK,CAAC,KAAK,KAAK,YAAY,OAAO,MAAM,KAAK,aAAa,KAAK,YAAY,QAAQ,OAAO,KAAK,CAAC,KAAK,KAAK,YAAY,QAAQ,OAAO,MAAM,OAAO,QAAQ,SAAS,KAAK,IAAI;AAE7K,UAAI,CAAC,KAAK,WAAW;AACnB;AAAA,MACF;AAEA,UAAI,IAAI,YAAY,KAAK;AACzB,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAIrC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,SAAU,OAAO;AAC9C,YAAI,UAAU,MAAM;AAClB;AAAA,QACF;AAEA;AAEA,YAAI,UAAU,MAAM;AAGlB,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACzB;AASA,aAAS,MAAM;AACb,UAAI;AAIJ,cAAQ,OAAO,YAAY,cAAc,cAAc,QAAQ,OAAO,OAAO,YAAY,QAAQ,QAAQ,WAAW,SAAS,IAAI,MAAM,UAAU,SAAS;AAAA,IAC5J;AASA,aAAS,KAAK,YAAY;AACxB,UAAI;AACF,YAAI,YAAY;AACd,kBAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QAC7C,OAAO;AACL,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACpC;AAAA,MACF,SAAS,OAAO;AAAA,MAEhB;AAAA,IACF;AASA,aAAS,OAAO;AACd,UAAI;AAEJ,UAAI;AACF,YAAI,QAAQ,QAAQ,QAAQ,OAAO;AAAA,MACrC,SAAS,OAAO;AAAA,MAAC;AAKjB,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC5D,YAAI,QAAQ,IAAI;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAaA,aAAS,eAAe;AACtB,UAAI;AAGF,eAAO;AAAA,MACT,SAAS,OAAO;AAAA,MAEhB;AAAA,IACF;AAEA,WAAO,UAAU,iBAAoB,OAAO;AAC5C,QAAI,aAAa,OAAO,QAAQ;AAKhC,eAAW,IAAI,SAAU,GAAG;AAC1B,UAAI;AACF,eAAO,KAAK,UAAU,CAAC;AAAA,MACzB,SAAS,OAAO;AACd,eAAO,iCAAiC,MAAM;AAAA,MAChD;AAAA,IACF;AAAA;AAAA;;;AClLA;AAAA;AAAA;AAEA,QAAI,MAAM;AAEV,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,yBAAyB;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA,MACf,WAAW,SAAS,KAAK;AACvB,YAAI,CAAC,KAAK;AACR,iBAAO;AAAA,QACT;AAEA,YAAI,IAAI,IAAI,IAAI,GAAG;AACnB,YAAI,EAAE,aAAa,SAAS;AAC1B,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,EAAE;AACb,YAAI,CAAC,MAAM;AACT,iBAAQ,EAAE,aAAa,WAAY,QAAQ;AAAA,QAC7C;AAEA,eAAO,EAAE,WAAW,OAAO,EAAE,WAAW,MAAM;AAAA,MAChD;AAAA,MAEA,eAAe,SAAS,GAAG,GAAG;AAC5B,YAAI,MAAM,KAAK,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC;AAChD,cAAM,QAAQ,GAAG,GAAG,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,MAEA,eAAe,SAAS,GAAG,GAAG;AAC5B,eAAQ,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC;AAAA,MAC5C;AAAA,MAEA,SAAS,SAAU,KAAK,MAAM;AAC5B,YAAI,KAAK,IAAI,MAAM,GAAG;AACtB,eAAO,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI;AAAA,MAC/C;AAAA,MAEA,UAAU,SAAU,KAAK,GAAG;AAC1B,eAAO,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAM,MAAM,IAAM,MAAM;AAAA,MAC7D;AAAA,MAEA,gBAAgB,SAAU,MAAM;AAC9B,eAAO,mDAAmD,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI;AAAA,MAC/F;AAAA,IACF;AAAA;AAAA;;;AClDA;AAAA;AAAA,QAAI,OAAO,OAAO,WAAW,YAAY;AAEvC,aAAO,UAAU,SAAS,SAAS,MAAM,WAAW;AAClD,YAAI,WAAW;AACb,eAAK,SAAS;AACd,eAAK,YAAY,OAAO,OAAO,UAAU,WAAW;AAAA,YAClD,aAAa;AAAA,cACX,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,OAAO;AAEL,aAAO,UAAU,SAAS,SAAS,MAAM,WAAW;AAClD,YAAI,WAAW;AACb,eAAK,SAAS;AACd,cAAI,WAAW,WAAY;AAAA,UAAC;AAC5B,mBAAS,YAAY,UAAU;AAC/B,eAAK,YAAY,IAAI,SAAS;AAC9B,eAAK,UAAU,cAAc;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAMA,aAAS,cAAc;AACrB,WAAK,aAAa,CAAC;AAAA,IACrB;AAEA,gBAAY,UAAU,mBAAmB,SAAS,WAAW,UAAU;AACrE,UAAI,EAAE,aAAa,KAAK,aAAa;AACnC,aAAK,WAAW,SAAS,IAAI,CAAC;AAAA,MAChC;AACA,UAAI,MAAM,KAAK,WAAW,SAAS;AAEnC,UAAI,IAAI,QAAQ,QAAQ,MAAM,IAAI;AAEhC,cAAM,IAAI,OAAO,CAAC,QAAQ,CAAC;AAAA,MAC7B;AACA,WAAK,WAAW,SAAS,IAAI;AAAA,IAC/B;AAEA,gBAAY,UAAU,sBAAsB,SAAS,WAAW,UAAU;AACxE,UAAI,MAAM,KAAK,WAAW,SAAS;AACnC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,UAAI,MAAM,IAAI,QAAQ,QAAQ;AAC9B,UAAI,QAAQ,IAAI;AACd,YAAI,IAAI,SAAS,GAAG;AAElB,eAAK,WAAW,SAAS,IAAI,IAAI,MAAM,GAAG,GAAG,EAAE,OAAO,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,QAC1E,OAAO;AACL,iBAAO,KAAK,WAAW,SAAS;AAAA,QAClC;AACA;AAAA,MACF;AAAA,IACF;AAEA,gBAAY,UAAU,gBAAgB,WAAW;AAC/C,UAAI,QAAQ,UAAU,CAAC;AACvB,UAAI,IAAI,MAAM;AAEd,UAAI,OAAO,UAAU,WAAW,IAAI,CAAC,KAAK,IAAI,MAAM,MAAM,MAAM,SAAS;AAKzE,UAAI,KAAK,OAAO,CAAC,GAAG;AAClB,aAAK,OAAO,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,MACjC;AACA,UAAI,KAAK,KAAK,YAAY;AAExB,YAAI,YAAY,KAAK,WAAW,CAAC;AACjC,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,oBAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7DjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,cAAc;AAGlB,aAAS,eAAe;AACtB,kBAAY,KAAK,IAAI;AAAA,IACvB;AAEA,aAAS,cAAc,WAAW;AAElC,iBAAa,UAAU,qBAAqB,SAAS,MAAM;AACzD,UAAI,MAAM;AACR,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B,OAAO;AACL,aAAK,aAAa,CAAC;AAAA,MACrB;AAAA,IACF;AAEA,iBAAa,UAAU,OAAO,SAAS,MAAM,UAAU;AACrD,UAAIC,QAAO,MACP,QAAQ;AAEZ,eAAS,IAAI;AACX,QAAAA,MAAK,eAAe,MAAM,CAAC;AAE3B,YAAI,CAAC,OAAO;AACV,kBAAQ;AACR,mBAAS,MAAM,MAAM,SAAS;AAAA,QAChC;AAAA,MACF;AAEA,WAAK,GAAG,MAAM,CAAC;AAAA,IACjB;AAEA,iBAAa,UAAU,OAAO,WAAW;AACvC,UAAI,OAAO,UAAU,CAAC;AACtB,UAAI,YAAY,KAAK,WAAW,IAAI;AACpC,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AAEA,UAAI,IAAI,UAAU;AAClB,UAAI,OAAO,IAAI,MAAM,IAAI,CAAC;AAC1B,eAAS,KAAK,GAAG,KAAK,GAAG,MAAM;AAC7B,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC7B;AACA,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAAA,MAC/B;AAAA,IACF;AAEA,iBAAa,UAAU,KAAK,aAAa,UAAU,cAAc,YAAY,UAAU;AACvF,iBAAa,UAAU,iBAAiB,YAAY,UAAU;AAE9D,WAAO,QAAQ,eAAe;AAAA;AAAA;;;ACxD9B;AAAA;AAAA;AAEA,QAAI,SAAS,OAAO,aAAa,OAAO;AACxC,QAAI,QAAQ;AACX,aAAO,UAAU,SAAS,uBAAuB,KAAK;AACrD,eAAO,IAAI,OAAO,GAAG;AAAA,MACtB;AAAA,IACD,OAAO;AACN,aAAO,UAAU;AAAA,IAClB;AAAA;AAAA;;;ACTA,IAAAC,qBAAA;AAAA;AAAA;AAEA,QAAI,QAAQ;AAAZ,QACI,WAAW;AADf,QAEI,WAAW;AAFf,QAGI,eAAe,kBAAkB;AAHrC,QAII,kBAAkB;AAGtB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,yBAAyB;AAAA,IACpD;AAEA,aAAS,mBAAmB,UAAU,QAAQ,SAAS;AACrD,UAAI,CAAC,mBAAmB,QAAQ,GAAG;AACjC,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAEA,mBAAa,KAAK,IAAI;AACtB,YAAM,eAAe,QAAQ;AAE7B,UAAIC,QAAO;AACX,UAAI,MAAM,SAAS,QAAQ,UAAU,YAAY;AACjD,UAAI,IAAI,MAAM,GAAG,CAAC,MAAM,SAAS;AAC/B,cAAM,QAAQ,IAAI,MAAM,CAAC;AAAA,MAC3B,OAAO;AACL,cAAM,OAAO,IAAI,MAAM,CAAC;AAAA,MAC1B;AACA,WAAK,MAAM;AAEX,WAAK,KAAK,IAAI,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO;AACnD,WAAK,GAAG,YAAY,SAAS,GAAG;AAC9B,cAAM,iBAAiB,EAAE,IAAI;AAC7B,QAAAA,MAAK,KAAK,WAAW,EAAE,IAAI;AAAA,MAC7B;AAOA,WAAK,YAAY,MAAM,UAAU,WAAW;AAC1C,cAAM,QAAQ;AACd,QAAAA,MAAK,GAAG,MAAM;AAAA,MAChB,CAAC;AACD,WAAK,GAAG,UAAU,SAAS,GAAG;AAC5B,cAAM,eAAe,EAAE,MAAM,EAAE,MAAM;AACrC,QAAAA,MAAK,KAAK,SAAS,EAAE,MAAM,EAAE,MAAM;AACnC,QAAAA,MAAK,SAAS;AAAA,MAChB;AACA,WAAK,GAAG,UAAU,SAAS,GAAG;AAC5B,cAAM,eAAe,CAAC;AACtB,QAAAA,MAAK,KAAK,SAAS,MAAM,6BAA6B;AACtD,QAAAA,MAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAEA,aAAS,oBAAoB,YAAY;AAEzC,uBAAmB,UAAU,OAAO,SAAS,MAAM;AACjD,UAAI,MAAM,MAAM,OAAO;AACvB,YAAM,QAAQ,GAAG;AACjB,WAAK,GAAG,KAAK,GAAG;AAAA,IAClB;AAEA,uBAAmB,UAAU,QAAQ,WAAW;AAC9C,YAAM,OAAO;AACb,UAAI,KAAK,KAAK;AACd,WAAK,SAAS;AACd,UAAI,IAAI;AACN,WAAG,MAAM;AAAA,MACX;AAAA,IACF;AAEA,uBAAmB,UAAU,WAAW,WAAW;AACjD,YAAM,UAAU;AAChB,UAAI,KAAK,KAAK;AACd,UAAI,IAAI;AACN,WAAG,YAAY,GAAG,UAAU,GAAG,UAAU;AAAA,MAC3C;AACA,YAAM,UAAU,KAAK,SAAS;AAC9B,WAAK,YAAY,KAAK,KAAK;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AAEA,uBAAmB,UAAU,WAAW;AACtC,YAAM,SAAS;AACf,aAAO,CAAC,CAAC;AAAA,IACX;AACA,uBAAmB,gBAAgB;AAMnC,uBAAmB,aAAa;AAEhC,WAAO,UAAU;AAAA;AAAA;;;AClGjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AAGrC,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,+BAA+B;AAAA,IAC1D;AAEA,aAAS,eAAe,KAAK,QAAQ;AACnC,YAAM,GAAG;AACT,mBAAa,KAAK,IAAI;AACtB,WAAK,aAAa,CAAC;AACnB,WAAK,SAAS;AACd,WAAK,MAAM;AAAA,IACb;AAEA,aAAS,gBAAgB,YAAY;AAErC,mBAAe,UAAU,OAAO,SAAS,SAAS;AAChD,YAAM,QAAQ,OAAO;AACrB,WAAK,WAAW,KAAK,OAAO;AAC5B,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAUA,mBAAe,UAAU,mBAAmB,WAAW;AACrD,YAAM,kBAAkB;AACxB,UAAIC,QAAO;AACX,UAAI;AACJ,WAAK,WAAW,WAAW;AACzB,cAAM,UAAU;AAChB,QAAAA,MAAK,WAAW;AAChB,qBAAa,IAAI;AAAA,MACnB;AACA,aAAO,WAAW,WAAW;AAC3B,cAAM,SAAS;AACf,QAAAA,MAAK,WAAW;AAChB,QAAAA,MAAK,aAAa;AAAA,MACpB,GAAG,EAAE;AAAA,IACP;AAEA,mBAAe,UAAU,eAAe,WAAW;AACjD,YAAM,gBAAgB,KAAK,WAAW,MAAM;AAC5C,UAAIA,QAAO;AACX,UAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,YAAI,UAAU,MAAM,KAAK,WAAW,KAAK,GAAG,IAAI;AAChD,aAAK,WAAW,KAAK,OAAO,KAAK,KAAK,SAAS,SAAS,KAAK;AAC3D,UAAAA,MAAK,WAAW;AAChB,cAAI,KAAK;AACP,kBAAM,SAAS,GAAG;AAClB,YAAAA,MAAK,KAAK,SAAS,IAAI,QAAQ,MAAM,oBAAoB,GAAG;AAC5D,YAAAA,MAAK,MAAM;AAAA,UACb,OAAO;AACL,YAAAA,MAAK,iBAAiB;AAAA,UACxB;AAAA,QACF,CAAC;AACD,aAAK,aAAa,CAAC;AAAA,MACrB;AAAA,IACF;AAEA,mBAAe,UAAU,WAAW,WAAW;AAC7C,YAAM,UAAU;AAChB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,mBAAe,UAAU,QAAQ,WAAW;AAC1C,YAAM,OAAO;AACb,WAAK,SAAS;AACd,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS;AACd,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtFjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AAGrC,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,uBAAuB;AAAA,IAClD;AAEA,aAAS,QAAQ,UAAU,YAAY,YAAY;AACjD,YAAM,UAAU;AAChB,mBAAa,KAAK,IAAI;AACtB,WAAK,WAAW;AAChB,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,kBAAkB;AAAA,IACzB;AAEA,aAAS,SAAS,YAAY;AAE9B,YAAQ,UAAU,oBAAoB,WAAW;AAC/C,YAAM,mBAAmB;AACzB,UAAIC,QAAO;AACX,UAAI,OAAO,KAAK,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY,KAAK,UAAU;AAEzE,WAAK,GAAG,WAAW,SAAS,KAAK;AAC/B,cAAM,WAAW,GAAG;AACpB,QAAAA,MAAK,KAAK,WAAW,GAAG;AAAA,MAC1B,CAAC;AAED,WAAK,KAAK,SAAS,SAAS,MAAM,QAAQ;AACxC,cAAM,SAAS,MAAM,QAAQA,MAAK,aAAa;AAC/C,QAAAA,MAAK,OAAO,OAAO;AAEnB,YAAI,CAACA,MAAK,eAAe;AACvB,cAAI,WAAW,WAAW;AACxB,YAAAA,MAAK,kBAAkB;AAAA,UACzB,OAAO;AACL,YAAAA,MAAK,KAAK,SAAS,QAAQ,MAAM,MAAM;AACvC,YAAAA,MAAK,mBAAmB;AAAA,UAC1B;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,YAAQ,UAAU,QAAQ,WAAW;AACnC,YAAM,OAAO;AACb,WAAK,mBAAmB;AACxB,WAAK,gBAAgB;AACrB,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,MAAM;AAAA,MAClB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxDjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,WAAW;AADf,QAEI,iBAAiB;AAFrB,QAGI,UAAU;AAGd,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,+BAA+B;AAAA,IAC1D;AAEA,aAAS,eAAe,UAAU,WAAW,YAAY,UAAU,YAAY;AAC7E,UAAI,UAAU,SAAS,QAAQ,UAAU,SAAS;AAClD,YAAM,OAAO;AACb,UAAIC,QAAO;AACX,qBAAe,KAAK,MAAM,UAAU,UAAU;AAE9C,WAAK,OAAO,IAAI,QAAQ,UAAU,SAAS,UAAU;AACrD,WAAK,KAAK,GAAG,WAAW,SAAS,KAAK;AACpC,cAAM,gBAAgB,GAAG;AACzB,QAAAA,MAAK,KAAK,WAAW,GAAG;AAAA,MAC1B,CAAC;AACD,WAAK,KAAK,KAAK,SAAS,SAAS,MAAM,QAAQ;AAC7C,cAAM,cAAc,MAAM,MAAM;AAChC,QAAAA,MAAK,OAAO;AACZ,QAAAA,MAAK,KAAK,SAAS,MAAM,MAAM;AAC/B,QAAAA,MAAK,MAAM;AAAA,MACb,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,cAAc;AAEvC,mBAAe,UAAU,QAAQ,WAAW;AAC1C,qBAAe,UAAU,MAAM,KAAK,IAAI;AACxC,YAAM,OAAO;AACb,WAAK,mBAAmB;AACxB,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,MAAM;AAChB,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5CjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,WAAW;AADf,QAEI,iBAAiB;AAGrB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,0BAA0B;AAAA,IACrD;AAEA,aAAS,iBAAiB,YAAY;AACpC,aAAO,SAAS,KAAK,SAAS,UAAU;AACtC,cAAM,sBAAsB,KAAK,OAAO;AACxC,YAAI,MAAM,CAAC;AACX,YAAI,OAAO,YAAY,UAAU;AAC/B,cAAI,UAAU,EAAC,gBAAgB,aAAY;AAAA,QAC7C;AACA,YAAI,UAAU,SAAS,QAAQ,KAAK,WAAW;AAC/C,YAAI,KAAK,IAAI,WAAW,QAAQ,SAAS,SAAS,GAAG;AACrD,WAAG,KAAK,UAAU,SAAS,QAAQ;AACjC,gBAAM,UAAU,MAAM;AACtB,eAAK;AAEL,cAAI,WAAW,OAAO,WAAW,KAAK;AACpC,mBAAO,SAAS,IAAI,MAAM,iBAAiB,MAAM,CAAC;AAAA,UACpD;AACA,mBAAS;AAAA,QACX,CAAC;AACD,eAAO,WAAW;AAChB,gBAAM,OAAO;AACb,aAAG,MAAM;AACT,eAAK;AAEL,cAAI,MAAM,IAAI,MAAM,SAAS;AAC7B,cAAI,OAAO;AACX,mBAAS,GAAG;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAEA,aAAS,mBAAmB,UAAU,WAAW,UAAU,YAAY;AACrE,qBAAe,KAAK,MAAM,UAAU,WAAW,iBAAiB,UAAU,GAAG,UAAU,UAAU;AAAA,IACnG;AAEA,aAAS,oBAAoB,cAAc;AAE3C,WAAO,UAAU;AAAA;AAAA;;;AChDjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AAGrC,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,4BAA4B;AAAA,IACvD;AAEA,aAAS,YAAY,KAAK,YAAY;AACpC,YAAM,GAAG;AACT,mBAAa,KAAK,IAAI;AACtB,UAAIC,QAAO;AAEX,WAAK,iBAAiB;AAEtB,WAAK,KAAK,IAAI,WAAW,QAAQ,KAAK,IAAI;AAC1C,WAAK,GAAG,GAAG,SAAS,KAAK,cAAc,KAAK,IAAI,CAAC;AACjD,WAAK,GAAG,KAAK,UAAU,SAAS,QAAQ,MAAM;AAC5C,cAAM,UAAU,QAAQ,IAAI;AAC5B,QAAAA,MAAK,cAAc,QAAQ,IAAI;AAC/B,QAAAA,MAAK,KAAK;AACV,YAAI,SAAS,WAAW,MAAM,YAAY;AAC1C,cAAM,SAAS,MAAM;AACrB,QAAAA,MAAK,KAAK,SAAS,MAAM,MAAM;AAC/B,QAAAA,MAAK,SAAS;AAAA,MAChB,CAAC;AAAA,IACH;AAEA,aAAS,aAAa,YAAY;AAElC,gBAAY,UAAU,gBAAgB,SAAS,QAAQ,MAAM;AAC3D,YAAM,iBAAiB,MAAM;AAC7B,UAAI,WAAW,OAAO,CAAC,MAAM;AAC3B;AAAA,MACF;AAEA,eAAS,MAAM,MAAM,KAAK,kBAAkB,MAAM,GAAG;AACnD,YAAI,MAAM,KAAK,MAAM,KAAK,cAAc;AACxC,cAAM,IAAI,QAAQ,IAAI;AACtB,YAAI,QAAQ,IAAI;AACd;AAAA,QACF;AACA,YAAI,MAAM,IAAI,MAAM,GAAG,GAAG;AAC1B,YAAI,KAAK;AACP,gBAAM,WAAW,GAAG;AACpB,eAAK,KAAK,WAAW,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,gBAAY,UAAU,WAAW,WAAW;AAC1C,YAAM,UAAU;AAChB,WAAK,mBAAmB;AAAA,IAC1B;AAEA,gBAAY,UAAU,QAAQ,WAAW;AACvC,YAAM,OAAO;AACb,UAAI,KAAK,IAAI;AACX,aAAK,GAAG,MAAM;AACd,cAAM,OAAO;AACb,aAAK,KAAK,SAAS,MAAM,MAAM;AAC/B,aAAK,KAAK;AAAA,MACZ;AACA,WAAK,SAAS;AAAA,IAChB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrEjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AADf,QAEI,QAAQ;AAFZ,QAGI,WAAW;AAHf,QAII,MAAM,OAAO;AAGjB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,2BAA2B;AAAA,IACtD;AAEA,aAAS,kBAAkB,QAAQ,KAAK,SAAS,MAAM;AACrD,YAAM,QAAQ,GAAG;AACjB,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,iBAAW,WAAY;AACrB,QAAAA,MAAK,OAAO,QAAQ,KAAK,SAAS,IAAI;AAAA,MACxC,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,mBAAmB,YAAY;AAExC,sBAAkB,UAAU,SAAS,SAAS,QAAQ,KAAK,SAAS,MAAM;AACxE,UAAIA,QAAO;AAEX,UAAI;AACF,aAAK,MAAM,IAAI,IAAI;AAAA,MACrB,SAAS,GAAG;AAAA,MAEZ;AAEA,UAAI,CAAC,KAAK,KAAK;AACb,cAAM,QAAQ;AACd,aAAK,KAAK,UAAU,GAAG,gBAAgB;AACvC,aAAK,SAAS;AACd;AAAA,MACF;AAGA,YAAM,SAAS,SAAS,KAAK,OAAQ,CAAC,oBAAI,KAAK,CAAE;AAIjD,WAAK,YAAY,MAAM,UAAU,WAAW;AAC1C,cAAM,gBAAgB;AACtB,QAAAA,MAAK,SAAS,IAAI;AAAA,MACpB,CAAC;AACD,UAAI;AACF,aAAK,IAAI,KAAK,QAAQ,KAAK,IAAI;AAC/B,YAAI,KAAK,WAAW,aAAa,KAAK,KAAK;AACzC,eAAK,IAAI,UAAU,KAAK;AACxB,eAAK,IAAI,YAAY,WAAW;AAC9B,kBAAM,aAAa;AACnB,YAAAA,MAAK,KAAK,UAAU,GAAG,EAAE;AACzB,YAAAA,MAAK,SAAS,KAAK;AAAA,UACrB;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AACV,cAAM,aAAa,CAAC;AAEpB,aAAK,KAAK,UAAU,GAAG,EAAE;AACzB,aAAK,SAAS,KAAK;AACnB;AAAA,MACF;AAEA,WAAK,CAAC,QAAQ,CAAC,KAAK,kBAAkB,kBAAkB,cAAc;AACpE,cAAM,iBAAiB;AAIvB,aAAK,IAAI,kBAAkB;AAAA,MAC7B;AACA,UAAI,QAAQ,KAAK,SAAS;AACxB,iBAAS,OAAO,KAAK,SAAS;AAC5B,eAAK,IAAI,iBAAiB,KAAK,KAAK,QAAQ,GAAG,CAAC;AAAA,QAClD;AAAA,MACF;AAEA,WAAK,IAAI,qBAAqB,WAAW;AACvC,YAAIA,MAAK,KAAK;AACZ,cAAI,IAAIA,MAAK;AACb,cAAI,MAAM;AACV,gBAAM,cAAc,EAAE,UAAU;AAChC,kBAAQ,EAAE,YAAY;AAAA,YACtB,KAAK;AAGH,kBAAI;AACF,yBAAS,EAAE;AACX,uBAAO,EAAE;AAAA,cACX,SAAS,GAAG;AAAA,cAEZ;AACA,oBAAM,UAAU,MAAM;AAEtB,kBAAI,WAAW,MAAM;AACnB,yBAAS;AAAA,cACX;AAGA,kBAAI,WAAW,OAAO,QAAQ,KAAK,SAAS,GAAG;AAC7C,sBAAM,OAAO;AACb,gBAAAA,MAAK,KAAK,SAAS,QAAQ,IAAI;AAAA,cACjC;AACA;AAAA,YACF,KAAK;AACH,uBAAS,EAAE;AACX,oBAAM,UAAU,MAAM;AAEtB,kBAAI,WAAW,MAAM;AACnB,yBAAS;AAAA,cACX;AAGA,kBAAI,WAAW,SAAS,WAAW,OAAO;AACxC,yBAAS;AAAA,cACX;AAEA,oBAAM,UAAU,QAAQ,EAAE,YAAY;AACtC,cAAAA,MAAK,KAAK,UAAU,QAAQ,EAAE,YAAY;AAC1C,cAAAA,MAAK,SAAS,KAAK;AACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI;AACF,QAAAA,MAAK,IAAI,KAAK,OAAO;AAAA,MACvB,SAAS,GAAG;AACV,QAAAA,MAAK,KAAK,UAAU,GAAG,EAAE;AACzB,QAAAA,MAAK,SAAS,KAAK;AAAA,MACrB;AAAA,IACF;AAEA,sBAAkB,UAAU,WAAW,SAAS,OAAO;AACrD,YAAM,SAAS;AACf,UAAI,CAAC,KAAK,KAAK;AACb;AAAA,MACF;AACA,WAAK,mBAAmB;AACxB,YAAM,UAAU,KAAK,SAAS;AAG9B,WAAK,IAAI,qBAAqB,WAAW;AAAA,MAAC;AAC1C,UAAI,KAAK,IAAI,WAAW;AACtB,aAAK,IAAI,YAAY;AAAA,MACvB;AAEA,UAAI,OAAO;AACT,YAAI;AACF,eAAK,IAAI,MAAM;AAAA,QACjB,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AACA,WAAK,YAAY,KAAK,MAAM;AAAA,IAC9B;AAEA,sBAAkB,UAAU,QAAQ,WAAW;AAC7C,YAAM,OAAO;AACb,WAAK,SAAS,IAAI;AAAA,IACpB;AAEA,sBAAkB,UAAU,CAAC,CAAC;AAG9B,QAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,QAAQ,EAAE,KAAK,GAAG;AAC9C,QAAI,CAAC,kBAAkB,WAAY,OAAO,QAAS;AACjD,YAAM,2BAA2B;AACjC,YAAM,WAAW;AACf,YAAI;AACF,iBAAO,IAAI,OAAO,GAAG,EAAE,mBAAmB;AAAA,QAC5C,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAAA,MACF;AACA,wBAAkB,UAAU,CAAC,CAAC,IAAI,IAAI;AAAA,IACxC;AAEA,QAAI,OAAO;AACX,QAAI;AACF,aAAO,qBAAqB,IAAI,IAAI;AAAA,IACtC,SAAS,SAAS;AAAA,IAElB;AAEA,sBAAkB,eAAe;AAEjC,WAAO,UAAU;AAAA;AAAA;;;AChMjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,YAAY;AAGhB,aAAS,cAAc,QAAQ,KAAK,SAAS,MAAM;AACjD,gBAAU,KAAK,MAAM,QAAQ,KAAK,SAAS,IAAI;AAAA,IACjD;AAEA,aAAS,eAAe,SAAS;AAEjC,kBAAc,UAAU,UAAU,WAAW,UAAU;AAEvD,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,YAAY;AAGhB,aAAS,eAAe,QAAQ,KAAK,SAAqB;AACxD,gBAAU,KAAK,MAAM,QAAQ,KAAK,SAAS;AAAA,QACzC,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,SAAS;AAElC,mBAAe,UAAU,UAAU;AAEnC,WAAO,UAAU;AAAA;AAAA;;;AChBjB,IAAAC,mBAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MACf,SAAS,WAAW;AAClB,eAAO,OAAO,aACZ,SAAS,KAAK,OAAO,UAAU,SAAS;AAAA,MAC5C;AAAA,MAEA,aAAa,WAAW;AACtB,eAAO,OAAO,aACZ,aAAa,KAAK,OAAO,UAAU,SAAS;AAAA,MAChD;AAAA,MAGA,WAAW,WAAY;AAErB,YAAI,CAAC,OAAO,UAAU;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI;AACF,iBAAO,CAAC,CAAC,OAAO,SAAS;AAAA,QAC3B,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,qBAAqB;AADzB,QAEI,cAAc;AAFlB,QAGI,gBAAgB;AAHpB,QAII,iBAAiB;AAJrB,QAKI,UAAU;AAGd,aAAS,sBAAsB,UAAU;AACvC,UAAI,CAAC,eAAe,WAAW,CAAC,cAAc,SAAS;AACrD,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,yBAAmB,KAAK,MAAM,UAAU,kBAAkB,aAAa,aAAa;AAAA,IACtF;AAEA,aAAS,uBAAuB,kBAAkB;AAElD,0BAAsB,UAAU,SAAS,MAAM;AAC7C,UAAI,KAAK,YAAY;AACnB,eAAO;AAAA,MACT;AAGA,UAAI,QAAQ,QAAQ,GAAG;AACrB,eAAO;AAAA,MACT;AAEA,aAAO,cAAc;AAAA,IACvB;AAEA,0BAAsB,gBAAgB;AACtC,0BAAsB,aAAa;AAKnC,0BAAsB,WAAW,CAAC,CAAC,OAAO;AAE1C,WAAO,UAAU;AAAA;AAAA;;;ACxCjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AADf,QAEI,aAAa;AAFjB,QAGI,UAAU;AAHd,QAII,WAAW;AAGf,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,0BAA0B;AAAA,IACrD;AAMA,aAAS,UAAU,QAAQ,KAAK,SAAS;AACvC,YAAM,QAAQ,GAAG;AACjB,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,iBAAW,WAAW;AACpB,QAAAA,MAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,MAClC,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,WAAW,YAAY;AAEhC,cAAU,UAAU,SAAS,SAAS,QAAQ,KAAK,SAAS;AAC1D,YAAM,QAAQ;AACd,UAAIA,QAAO;AACX,UAAI,MAAM,IAAI,OAAO,eAAe;AAEpC,YAAM,SAAS,SAAS,KAAK,OAAQ,CAAC,oBAAI,KAAK,CAAE;AAEjD,UAAI,UAAU,WAAW;AACvB,cAAM,SAAS;AACf,QAAAA,MAAK,OAAO;AAAA,MACd;AACA,UAAI,YAAY,WAAW;AACzB,cAAM,WAAW;AACjB,QAAAA,MAAK,OAAO;AAAA,MACd;AACA,UAAI,aAAa,WAAW;AAC1B,cAAM,YAAY,IAAI,YAAY;AAClC,QAAAA,MAAK,KAAK,SAAS,KAAK,IAAI,YAAY;AAAA,MAC1C;AACA,UAAI,SAAS,WAAW;AACtB,cAAM,MAAM;AACZ,QAAAA,MAAK,KAAK,UAAU,KAAK,IAAI,YAAY;AACzC,QAAAA,MAAK,SAAS,KAAK;AAAA,MACrB;AACA,WAAK,MAAM;AACX,WAAK,YAAY,WAAW,UAAU,WAAW;AAC/C,QAAAA,MAAK,SAAS,IAAI;AAAA,MACpB,CAAC;AACD,UAAI;AAEF,aAAK,IAAI,KAAK,QAAQ,GAAG;AACzB,YAAI,KAAK,SAAS;AAChB,eAAK,IAAI,UAAU,KAAK;AAAA,QAC1B;AACA,aAAK,IAAI,KAAK,OAAO;AAAA,MACvB,SAAS,GAAG;AACV,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEA,cAAU,UAAU,SAAS,WAAW;AACtC,WAAK,KAAK,UAAU,GAAG,EAAE;AACzB,WAAK,SAAS,KAAK;AAAA,IACrB;AAEA,cAAU,UAAU,WAAW,SAAS,OAAO;AAC7C,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,KAAK,KAAK;AACb;AAAA,MACF;AACA,WAAK,mBAAmB;AACxB,iBAAW,UAAU,KAAK,SAAS;AAEnC,WAAK,IAAI,YAAY,KAAK,IAAI,UAAU,KAAK,IAAI,aAAa,KAAK,IAAI,SAAS;AAChF,UAAI,OAAO;AACT,YAAI;AACF,eAAK,IAAI,MAAM;AAAA,QACjB,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AACA,WAAK,YAAY,KAAK,MAAM;AAAA,IAC9B;AAEA,cAAU,UAAU,QAAQ,WAAW;AACrC,YAAM,OAAO;AACb,WAAK,SAAS,IAAI;AAAA,IACpB;AAGA,cAAU,UAAU,CAAC,EAAE,OAAO,kBAAkB,QAAQ,UAAU;AAElE,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,qBAAqB;AADzB,QAEI,cAAc;AAFlB,QAGI,YAAY;AAOhB,aAAS,sBAAsB,UAAU;AACvC,UAAI,CAAC,UAAU,SAAS;AACtB,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,yBAAmB,KAAK,MAAM,UAAU,kBAAkB,aAAa,SAAS;AAAA,IAClF;AAEA,aAAS,uBAAuB,kBAAkB;AAElD,0BAAsB,UAAU,SAAS,MAAM;AAC7C,UAAI,KAAK,iBAAiB,KAAK,YAAY;AACzC,eAAO;AAAA,MACT;AACA,aAAO,UAAU,WAAW,KAAK;AAAA,IACnC;AAEA,0BAAsB,gBAAgB;AACtC,0BAAsB,aAAa;AAEnC,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,WAAO,UAAU,OAAO;AAAA;AAAA;;;ACAxB,IAAAC,uBAAA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AADrC,QAEI,oBAAoB;AAGxB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,oCAAoC;AAAA,IAC/D;AAEA,aAAS,oBAAoB,KAAK;AAChC,YAAM,GAAG;AACT,mBAAa,KAAK,IAAI;AAEtB,UAAIC,QAAO;AACX,UAAI,KAAK,KAAK,KAAK,IAAI,kBAAkB,GAAG;AAC5C,SAAG,YAAY,SAAS,GAAG;AACzB,cAAM,WAAW,EAAE,IAAI;AACvB,QAAAA,MAAK,KAAK,WAAW,UAAU,EAAE,IAAI,CAAC;AAAA,MACxC;AACA,SAAG,UAAU,SAAS,GAAG;AACvB,cAAM,SAAS,GAAG,YAAY,CAAC;AAG/B,YAAI,SAAU,GAAG,eAAe,IAAI,YAAY;AAChD,QAAAA,MAAK,SAAS;AACd,QAAAA,MAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF;AAEA,aAAS,qBAAqB,YAAY;AAE1C,wBAAoB,UAAU,QAAQ,WAAW;AAC/C,YAAM,OAAO;AACb,WAAK,SAAS;AACd,WAAK,OAAO,MAAM;AAAA,IACpB;AAEA,wBAAoB,UAAU,WAAW,WAAW;AAClD,YAAM,SAAS;AACf,UAAI,KAAK,KAAK;AACd,UAAI,IAAI;AACN,WAAG,YAAY,GAAG,UAAU;AAC5B,WAAG,MAAM;AACT,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAEA,wBAAoB,UAAU,SAAS,SAAS,QAAQ;AACtD,YAAM,SAAS,MAAM;AACrB,UAAIA,QAAO;AAIX,iBAAW,WAAW;AACpB,QAAAA,MAAK,KAAK,SAAS,MAAM,MAAM;AAC/B,QAAAA,MAAK,mBAAmB;AAAA,MAC1B,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9DjB,IAAAC,uBAAA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,qBAAqB;AADzB,QAEI,sBAAsB;AAF1B,QAGI,gBAAgB;AAHpB,QAII,oBAAoB;AAGxB,aAAS,qBAAqB,UAAU;AACtC,UAAI,CAAC,qBAAqB,QAAQ,GAAG;AACnC,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AAEA,yBAAmB,KAAK,MAAM,UAAU,gBAAgB,qBAAqB,aAAa;AAAA,IAC5F;AAEA,aAAS,sBAAsB,kBAAkB;AAEjD,yBAAqB,UAAU,WAAW;AACxC,aAAO,CAAC,CAAC;AAAA,IACX;AAEA,yBAAqB,gBAAgB;AACrC,yBAAqB,aAAa;AAElC,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA;AAEA,QAAI,aAAa;AAAjB,QACI,UAAU;AAGd,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,4BAA4B;AAAA,IACvD;AAEA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,MACT,iBAAiB;AAAA,MAEjB,wBAAwB,WAAW;AACjC,YAAI,EAAE,OAAO,QAAQ,WAAW,SAAS;AACvC,iBAAO,OAAO,QAAQ,OAAO,IAAI,CAAC;AAAA,QACpC;AAAA,MACF;AAAA,MAEA,aAAa,SAAS,MAAM,MAAM;AAChC,YAAI,OAAO,WAAW,QAAQ;AAC5B,iBAAO,OAAO,YAAY,KAAK,UAAU;AAAA,YACvC,UAAU,OAAO,QAAQ;AAAA,YACzB;AAAA,YACA,MAAM,QAAQ;AAAA,UAChB,CAAC,GAAG,GAAG;AAAA,QACT,OAAO;AACL,gBAAM,yCAAyC,MAAM,IAAI;AAAA,QAC3D;AAAA,MACF;AAAA,MAEA,cAAc,SAAS,WAAW,eAAe;AAC/C,YAAI,SAAS,OAAO,SAAS,cAAc,QAAQ;AACnD,YAAI,MAAM;AACV,YAAI,WAAW,WAAW;AACxB,gBAAM,UAAU;AAChB,uBAAa,IAAI;AAEjB,cAAI;AACF,mBAAO,SAAS;AAAA,UAClB,SAAS,GAAG;AAAA,UAEZ;AACA,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,UAAU,WAAW;AACvB,gBAAM,SAAS;AACf,cAAI,QAAQ;AACV,qBAAS;AAIT,uBAAW,WAAW;AACpB,kBAAI,QAAQ;AACV,uBAAO,WAAW,YAAY,MAAM;AAAA,cACtC;AACA,uBAAS;AAAA,YACX,GAAG,CAAC;AACJ,uBAAW,UAAU,SAAS;AAAA,UAChC;AAAA,QACF;AACA,YAAI,UAAU,SAAS,KAAK;AAC1B,gBAAM,WAAW,GAAG;AACpB,cAAI,QAAQ;AACV,oBAAQ;AACR,0BAAc,GAAG;AAAA,UACnB;AAAA,QACF;AACA,YAAI,OAAO,SAAS,KAAK,QAAQ;AAC/B,gBAAM,QAAQ,KAAK,MAAM;AACzB,qBAAW,WAAW;AACpB,gBAAI;AAGF,kBAAI,UAAU,OAAO,eAAe;AAClC,uBAAO,cAAc,YAAY,KAAK,MAAM;AAAA,cAC9C;AAAA,YACF,SAAS,GAAG;AAAA,YAEZ;AAAA,UACF,GAAG,CAAC;AAAA,QACN;AAEA,eAAO,MAAM;AACb,eAAO,MAAM,UAAU;AACvB,eAAO,MAAM,WAAW;AACxB,eAAO,UAAU,WAAW;AAC1B,kBAAQ,SAAS;AAAA,QACnB;AACA,eAAO,SAAS,WAAW;AACzB,gBAAM,QAAQ;AAGd,uBAAa,IAAI;AACjB,iBAAO,WAAW,WAAW;AAC3B,oBAAQ,gBAAgB;AAAA,UAC1B,GAAG,GAAI;AAAA,QACT;AACA,eAAO,SAAS,KAAK,YAAY,MAAM;AACvC,eAAO,WAAW,WAAW;AAC3B,kBAAQ,SAAS;AAAA,QACnB,GAAG,IAAK;AACR,oBAAY,WAAW,UAAU,OAAO;AACxC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MAGA,gBAAgB,SAAS,WAAW,eAAe;AACjD,YAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,QAAQ,EAAE,KAAK,GAAG;AAC9C,YAAI,MAAM,IAAI,OAAO,GAAG,EAAE,UAAU;AACpC,YAAI,MAAM;AACV,YAAI;AACJ,YAAI,WAAW,WAAW;AACxB,uBAAa,IAAI;AACjB,iBAAO,UAAU;AAAA,QACnB;AACA,YAAI,UAAU,WAAW;AACvB,cAAI,KAAK;AACP,qBAAS;AACT,uBAAW,UAAU,SAAS;AAC9B,mBAAO,WAAW,YAAY,MAAM;AACpC,qBAAS,MAAM;AACf,2BAAe;AAAA,UACjB;AAAA,QACF;AACA,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,WAAW,CAAC;AAClB,cAAI,KAAK;AACP,oBAAQ;AACR,0BAAc,CAAC;AAAA,UACjB;AAAA,QACF;AACA,YAAI,OAAO,SAAS,KAAK,QAAQ;AAC/B,cAAI;AAGF,uBAAW,WAAW;AACpB,kBAAI,UAAU,OAAO,eAAe;AAChC,uBAAO,cAAc,YAAY,KAAK,MAAM;AAAA,cAChD;AAAA,YACF,GAAG,CAAC;AAAA,UACN,SAAS,GAAG;AAAA,UAEZ;AAAA,QACF;AAEA,YAAI,KAAK;AACT,YAAI,MAAM,oCACsB,OAAO,SAAS,SAAS,qBACxB;AACjC,YAAI,MAAM;AACV,YAAI,aAAa,OAAO,QAAQ,OAAO,IAAI,OAAO,OAAO,QAAQ,OAAO;AACxE,YAAI,IAAI,IAAI,cAAc,KAAK;AAC/B,YAAI,KAAK,YAAY,CAAC;AACtB,iBAAS,IAAI,cAAc,QAAQ;AACnC,UAAE,YAAY,MAAM;AACpB,eAAO,MAAM;AACb,eAAO,UAAU,WAAW;AAC1B,kBAAQ,SAAS;AAAA,QACnB;AACA,eAAO,WAAW,WAAW;AAC3B,kBAAQ,SAAS;AAAA,QACnB,GAAG,IAAK;AACR,oBAAY,WAAW,UAAU,OAAO;AACxC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEA,WAAO,QAAQ,gBAAgB;AAC/B,QAAI,OAAO,UAAU;AAGnB,aAAO,QAAQ,iBAAiB,OAAO,OAAO,gBAAgB,cAC5D,OAAO,OAAO,gBAAgB,aAAc,CAAC,QAAQ,YAAY;AAAA,IACrE;AAAA;AAAA;;;ACxLA,IAAAC,kBAAA;AAAA;AAAA;AAUA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AADrC,QAEI,UAAU;AAFd,QAGI,WAAW;AAHf,QAII,cAAc;AAJlB,QAKI,aAAa;AALjB,QAMI,SAAS;AAGb,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,gCAAgC;AAAA,IAC3D;AAEA,aAAS,gBAAgB,WAAW,UAAU,SAAS;AACrD,UAAI,CAAC,gBAAgB,QAAQ,GAAG;AAC9B,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,mBAAa,KAAK,IAAI;AAEtB,UAAIC,QAAO;AACX,WAAK,SAAS,SAAS,UAAU,OAAO;AACxC,WAAK,UAAU;AACf,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,WAAW,OAAO,OAAO,CAAC;AAE/B,UAAI,YAAY,SAAS,QAAQ,SAAS,cAAc,IAAI,MAAM,KAAK;AACvE,YAAM,WAAW,UAAU,SAAS;AAEpC,WAAK,YAAY,YAAY,aAAa,WAAW,SAAS,GAAG;AAC/D,cAAM,cAAc;AACpB,QAAAA,MAAK,KAAK,SAAS,MAAM,+BAA+B,IAAI,GAAG;AAC/D,QAAAA,MAAK,MAAM;AAAA,MACb,CAAC;AAED,WAAK,oBAAoB,KAAK,SAAS,KAAK,IAAI;AAChD,iBAAW,YAAY,WAAW,KAAK,iBAAiB;AAAA,IAC1D;AAEA,aAAS,iBAAiB,YAAY;AAEtC,oBAAgB,UAAU,QAAQ,WAAW;AAC3C,YAAM,OAAO;AACb,WAAK,mBAAmB;AACxB,UAAI,KAAK,WAAW;AAClB,mBAAW,YAAY,WAAW,KAAK,iBAAiB;AACxD,YAAI;AAGF,eAAK,YAAY,GAAG;AAAA,QACtB,SAAS,GAAG;AAAA,QAEZ;AACA,aAAK,UAAU,QAAQ;AACvB,aAAK,YAAY;AACjB,aAAK,oBAAoB,KAAK,YAAY;AAAA,MAC5C;AAAA,IACF;AAEA,oBAAgB,UAAU,WAAW,SAAS,GAAG;AAC/C,YAAM,WAAW,EAAE,IAAI;AACvB,UAAI,CAAC,SAAS,cAAc,EAAE,QAAQ,KAAK,MAAM,GAAG;AAClD,cAAM,mBAAmB,EAAE,QAAQ,KAAK,MAAM;AAC9C;AAAA,MACF;AAEA,UAAI;AACJ,UAAI;AACF,wBAAgB,KAAK,MAAM,EAAE,IAAI;AAAA,MACnC,SAAS,SAAS;AAChB,cAAM,YAAY,EAAE,IAAI;AACxB;AAAA,MACF;AAEA,UAAI,cAAc,aAAa,KAAK,UAAU;AAC5C,cAAM,wBAAwB,cAAc,UAAU,KAAK,QAAQ;AACnE;AAAA,MACF;AAEA,cAAQ,cAAc,MAAM;AAAA,QAC5B,KAAK;AACH,eAAK,UAAU,OAAO;AAEtB,eAAK,YAAY,KAAK,KAAK,UAAU;AAAA,YACnC;AAAA,YACA,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,UACP,CAAC,CAAC;AACF;AAAA,QACF,KAAK;AACH,eAAK,KAAK,WAAW,cAAc,IAAI;AACvC;AAAA,QACF,KAAK;AACH,cAAI;AACJ,cAAI;AACF,oBAAQ,KAAK,MAAM,cAAc,IAAI;AAAA,UACvC,SAAS,SAAS;AAChB,kBAAM,YAAY,cAAc,IAAI;AACpC;AAAA,UACF;AACA,eAAK,KAAK,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACrC,eAAK,MAAM;AACX;AAAA,MACF;AAAA,IACF;AAEA,oBAAgB,UAAU,cAAc,SAAS,MAAM,MAAM;AAC3D,YAAM,eAAe,MAAM,IAAI;AAC/B,WAAK,UAAU,KAAK,KAAK,UAAU;AAAA,QACjC,UAAU,KAAK;AAAA,QACf;AAAA,QACA,MAAM,QAAQ;AAAA,MAChB,CAAC,GAAG,KAAK,MAAM;AAAA,IACjB;AAEA,oBAAgB,UAAU,OAAO,SAAS,SAAS;AACjD,YAAM,QAAQ,OAAO;AACrB,WAAK,YAAY,KAAK,OAAO;AAAA,IAC/B;AAEA,oBAAgB,UAAU,WAAW;AACnC,aAAO,YAAY;AAAA,IACrB;AAEA,oBAAgB,gBAAgB;AAChC,oBAAgB,aAAa;AAE7B,WAAO,UAAU;AAAA;AAAA;;;AC3IjB;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MACf,UAAU,SAAS,KAAK;AACtB,YAAI,OAAO,OAAO;AAClB,eAAO,SAAS,cAAc,SAAS,YAAY,CAAC,CAAC;AAAA,MACvD;AAAA,MAEA,QAAQ,SAAS,KAAK;AACpB,YAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,SAAS,UAAU,QAAQ,IAAI,QAAQ,KAAK;AAC1D,mBAAS,UAAU,CAAC;AACpB,eAAK,QAAQ,QAAQ;AACnB,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,IAAI,GAAG;AACtD,kBAAI,IAAI,IAAI,OAAO,IAAI;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACvBA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,kBAAkB;AADtB,QAEI,cAAc;AAGlB,WAAO,UAAU,SAAS,WAAW;AAEnC,eAAS,oBAAoB,UAAU,SAAS;AAC9C,wBAAgB,KAAK,MAAM,UAAU,eAAe,UAAU,OAAO;AAAA,MACvE;AAEA,eAAS,qBAAqB,eAAe;AAE7C,0BAAoB,UAAU,SAAS,KAAK,MAAM;AAChD,YAAI,CAAC,OAAO,UAAU;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,aAAa,YAAY,OAAO,CAAC,GAAG,IAAI;AAC5C,mBAAW,aAAa;AACxB,eAAO,UAAU,QAAQ,UAAU,KAAK,gBAAgB,QAAQ;AAAA,MAClE;AAEA,0BAAoB,gBAAgB,YAAY,UAAU;AAC1D,0BAAoB,WAAW;AAC/B,0BAAoB,aAAa,gBAAgB,aAAa,UAAU,aAAa;AAErF,0BAAoB,kBAAkB;AAEtC,aAAO;AAAA,IACT;AAAA;AAAA;;;AChCA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,cAAc;AADlB,QAEI,WAAW;AAFf,QAGI,eAAe,kBAAkB;AAHrC,QAII,SAAS;AAGb,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,iCAAiC;AAAA,IAC5D;AAEA,aAAS,iBAAiB,KAAK;AAC7B,YAAM,GAAG;AACT,mBAAa,KAAK,IAAI;AACtB,UAAIC,QAAO;AACX,kBAAY,uBAAuB;AAEnC,WAAK,KAAK,MAAM,OAAO,OAAO,CAAC;AAC/B,YAAM,SAAS,SAAS,KAAK,OAAO,mBAAmB,YAAY,UAAU,MAAM,KAAK,EAAE,CAAC;AAE3F,YAAM,kBAAkB,iBAAiB,eAAe;AACxD,UAAI,gBAAgB,iBAAiB,kBACjC,YAAY,iBAAiB,YAAY;AAE7C,aAAO,YAAY,OAAO,EAAE,KAAK,EAAE,IAAI;AAAA,QACrC,OAAO,WAAW;AAChB,gBAAM,OAAO;AACb,UAAAA,MAAK,UAAU,OAAO;AAAA,QACxB;AAAA,QACA,SAAS,SAAS,MAAM;AACtB,gBAAM,WAAW,IAAI;AACrB,UAAAA,MAAK,KAAK,WAAW,IAAI;AAAA,QAC3B;AAAA,QACA,MAAM,WAAW;AACf,gBAAM,MAAM;AACZ,UAAAA,MAAK,SAAS;AACd,UAAAA,MAAK,OAAO,SAAS;AAAA,QACvB;AAAA,MACF;AACA,WAAK,YAAY,cAAc,KAAK,WAAW;AAC7C,cAAM,UAAU;AAChB,QAAAA,MAAK,SAAS;AACd,QAAAA,MAAK,OAAO,WAAW;AAAA,MACzB,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,YAAY;AAEvC,qBAAiB,UAAU,QAAQ,WAAW;AAC5C,YAAM,OAAO;AACb,WAAK,SAAS;AACd,WAAK,OAAO,MAAM;AAAA,IACpB;AAEA,qBAAiB,UAAU,WAAW,WAAW;AAC/C,YAAM,UAAU;AAChB,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,QAAQ;AACvB,aAAK,YAAY;AAAA,MACnB;AACA,aAAO,OAAO,YAAY,OAAO,EAAE,KAAK,EAAE;AAAA,IAC5C;AAEA,qBAAiB,UAAU,SAAS,SAAS,QAAQ;AACnD,YAAM,UAAU,MAAM;AACtB,WAAK,KAAK,SAAS,MAAM,MAAM;AAC/B,WAAK,mBAAmB;AAAA,IAC1B;AAEA,qBAAiB,kBAAkB;AAGnC,QAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,QAAQ,EAAE,KAAK,GAAG;AAC9C,QAAI,OAAO,QAAQ;AACjB,UAAI;AACF,yBAAiB,kBAAkB,CAAC,CAAC,IAAI,OAAO,GAAG,EAAE,UAAU;AAAA,MACjE,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAEA,qBAAiB,UAAU,iBAAiB,mBAAmB,YAAY;AAE3E,WAAO,UAAU;AAAA;AAAA;;;ACtFjB,IAAAC,oBAAA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,mBAAmB;AADvB,QAEI,iBAAiB;AAFrB,QAGI,qBAAqB;AAGzB,aAAS,kBAAkB,UAAU;AACnC,UAAI,CAAC,iBAAiB,SAAS;AAC7B,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,yBAAmB,KAAK,MAAM,UAAU,aAAa,kBAAkB,cAAc;AAAA,IACvF;AAEA,aAAS,mBAAmB,kBAAkB;AAE9C,sBAAkB,UAAU,SAAS,MAAM;AACzC,aAAO,iBAAiB,WAAW,KAAK;AAAA,IAC1C;AAEA,sBAAkB,gBAAgB;AAClC,sBAAkB,aAAa;AAE/B,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,qBAAqB;AADzB,QAEI,cAAc;AAFlB,QAGI,gBAAgB;AAHpB,QAII,iBAAiB;AAGrB,aAAS,oBAAoB,UAAU;AACrC,UAAI,CAAC,eAAe,WAAW,CAAC,cAAc,SAAS;AACrD,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,yBAAmB,KAAK,MAAM,UAAU,QAAQ,aAAa,aAAa;AAAA,IAC5E;AAEA,aAAS,qBAAqB,kBAAkB;AAEhD,wBAAoB,UAAU,SAAS,MAAM;AAC3C,UAAI,KAAK,YAAY;AACnB,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,WAAW,KAAK,YAAY;AAC7C,eAAO;AAAA,MACT;AACA,aAAO,cAAc;AAAA,IACvB;AAEA,wBAAoB,gBAAgB;AACpC,wBAAoB,aAAa;AAEjC,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,qBAAqB;AADzB,QAEI,wBAAwB;AAF5B,QAGI,cAAc;AAHlB,QAII,YAAY;AAGhB,aAAS,oBAAoB,UAAU;AACrC,UAAI,CAAC,UAAU,SAAS;AACtB,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,yBAAmB,KAAK,MAAM,UAAU,QAAQ,aAAa,SAAS;AAAA,IACxE;AAEA,aAAS,qBAAqB,kBAAkB;AAEhD,wBAAoB,UAAU,sBAAsB;AACpD,wBAAoB,gBAAgB;AACpC,wBAAoB,aAAa;AAEjC,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA;AAEA,QAAI,QAAQ;AAAZ,QACI,SAAS;AADb,QAEI,UAAU;AAFd,QAGI,WAAW;AAHf,QAII,WAAW;AAJf,QAKI,eAAe,kBAAkB;AAGrC,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,8BAA8B;AAAA,IACzD;AAEA,aAAS,cAAc,KAAK;AAC1B,YAAM,GAAG;AACT,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,YAAM,uBAAuB;AAE7B,WAAK,KAAK,MAAM,OAAO,OAAO,CAAC;AAC/B,UAAI,YAAY,SAAS,SAAS,KAAK,OAAO,mBAAmB,MAAM,UAAU,MAAM,KAAK,EAAE,CAAC;AAE/F,aAAO,MAAM,OAAO,EAAE,KAAK,EAAE,IAAI,KAAK,UAAU,KAAK,IAAI;AACzD,WAAK,cAAc,SAAS;AAG5B,WAAK,YAAY,WAAW,WAAW;AACrC,cAAM,SAAS;AACf,QAAAA,MAAK,OAAO,IAAI,MAAM,0CAA0C,CAAC;AAAA,MACnE,GAAG,cAAc,OAAO;AAAA,IAC1B;AAEA,aAAS,eAAe,YAAY;AAEpC,kBAAc,UAAU,QAAQ,WAAW;AACzC,YAAM,OAAO;AACb,UAAI,OAAO,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG;AAClC,YAAI,MAAM,IAAI,MAAM,yBAAyB;AAC7C,YAAI,OAAO;AACX,aAAK,OAAO,GAAG;AAAA,MACjB;AAAA,IACF;AAEA,kBAAc,UAAU;AACxB,kBAAc,qBAAqB;AAEnC,kBAAc,UAAU,YAAY,SAAS,MAAM;AACjD,YAAM,aAAa,IAAI;AACvB,WAAK,SAAS;AAEd,UAAI,KAAK,UAAU;AACjB;AAAA,MACF;AAEA,UAAI,MAAM;AACR,cAAM,WAAW,IAAI;AACrB,aAAK,KAAK,WAAW,IAAI;AAAA,MAC3B;AACA,WAAK,KAAK,SAAS,MAAM,SAAS;AAClC,WAAK,mBAAmB;AAAA,IAC1B;AAEA,kBAAc,UAAU,SAAS,SAAS,KAAK;AAC7C,YAAM,UAAU,GAAG;AACnB,WAAK,SAAS;AACd,WAAK,WAAW;AAChB,WAAK,KAAK,SAAS,IAAI,MAAM,IAAI,OAAO;AACxC,WAAK,mBAAmB;AAAA,IAC1B;AAEA,kBAAc,UAAU,WAAW,WAAW;AAC5C,YAAM,UAAU;AAChB,mBAAa,KAAK,SAAS;AAC3B,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,WAAW,YAAY,KAAK,OAAO;AAChD,aAAK,UAAU;AAAA,MACjB;AACA,UAAI,KAAK,QAAQ;AACf,YAAI,SAAS,KAAK;AAGlB,eAAO,WAAW,YAAY,MAAM;AACpC,eAAO,qBAAqB,OAAO,UAC/B,OAAO,SAAS,OAAO,UAAU;AACrC,aAAK,SAAS;AAAA,MAChB;AACA,aAAO,OAAO,MAAM,OAAO,EAAE,KAAK,EAAE;AAAA,IACtC;AAEA,kBAAc,UAAU,eAAe,WAAW;AAChD,YAAM,cAAc;AACpB,UAAIA,QAAO;AACX,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AAEA,WAAK,aAAa,WAAW,WAAW;AACtC,YAAI,CAACA,MAAK,YAAY;AACpB,UAAAA,MAAK,OAAO,IAAI,MAAM,0CAA0C,CAAC;AAAA,QACnE;AAAA,MACF,GAAG,cAAc,kBAAkB;AAAA,IACrC;AAEA,kBAAc,UAAU,gBAAgB,SAAS,KAAK;AACpD,YAAM,iBAAiB,GAAG;AAC1B,UAAIA,QAAO;AACX,UAAI,SAAS,KAAK,SAAS,OAAO,SAAS,cAAc,QAAQ;AACjE,UAAI;AAEJ,aAAO,KAAK,MAAM,OAAO,OAAO,CAAC;AACjC,aAAO,MAAM;AACb,aAAO,OAAO;AACd,aAAO,UAAU;AACjB,aAAO,UAAU,KAAK,aAAa,KAAK,IAAI;AAC5C,aAAO,SAAS,WAAW;AACzB,cAAM,QAAQ;AACd,QAAAA,MAAK,OAAO,IAAI,MAAM,yCAAyC,CAAC;AAAA,MAClE;AAIA,aAAO,qBAAqB,WAAW;AACrC,cAAM,sBAAsB,OAAO,UAAU;AAC7C,YAAI,gBAAgB,KAAK,OAAO,UAAU,GAAG;AAC3C,cAAI,UAAU,OAAO,WAAW,OAAO,SAAS;AAC9C,YAAAA,MAAK,aAAa;AAClB,gBAAI;AAEF,qBAAO,QAAQ;AAAA,YACjB,SAAS,GAAG;AAAA,YAEZ;AAAA,UACF;AACA,cAAI,QAAQ;AACV,YAAAA,MAAK,OAAO,IAAI,MAAM,qDAAqD,CAAC;AAAA,UAC9E;AAAA,QACF;AAAA,MACF;AAWA,UAAI,OAAO,OAAO,UAAU,eAAe,OAAO,SAAS,aAAa;AAItE,YAAI,CAAC,QAAQ,QAAQ,GAAG;AAEtB,cAAI;AACF,mBAAO,UAAU,OAAO;AACxB,mBAAO,QAAQ;AAAA,UACjB,SAAS,GAAG;AAAA,UAEZ;AACA,iBAAO,QAAQ;AAAA,QACjB,OAAO;AAEL,oBAAU,KAAK,UAAU,OAAO,SAAS,cAAc,QAAQ;AAC/D,kBAAQ,OAAO,0CAA0C,OAAO,KAAK;AACrE,iBAAO,QAAQ,QAAQ,QAAQ;AAAA,QACjC;AAAA,MACF;AACA,UAAI,OAAO,OAAO,UAAU,aAAa;AACvC,eAAO,QAAQ;AAAA,MACjB;AAEA,UAAI,OAAO,OAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACzD,WAAK,aAAa,QAAQ,KAAK,UAAU;AACzC,UAAI,SAAS;AACX,aAAK,aAAa,SAAS,KAAK,UAAU;AAAA,MAC5C;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtLjB,IAAAC,iBAAA;AAAA;AAAA;AAEA,QAAI,SAAS;AAAb,QACI,WAAW;AAGf,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,4BAA4B;AAAA,IACvD;AAEA,QAAI;AAAJ,QAAU;AAEV,aAAS,aAAa,IAAI;AACxB,YAAM,gBAAgB,EAAE;AACxB,UAAI;AAEF,eAAO,OAAO,SAAS,cAAc,mBAAmB,KAAK,IAAI;AAAA,MACnE,SAAS,GAAG;AACV,YAAI,SAAS,OAAO,SAAS,cAAc,QAAQ;AACnD,eAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,aAAa;AACpB,YAAM,YAAY;AAClB,aAAO,OAAO,SAAS,cAAc,MAAM;AAC3C,WAAK,MAAM,UAAU;AACrB,WAAK,MAAM,WAAW;AACtB,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,gBAAgB;AAErB,aAAO,OAAO,SAAS,cAAc,UAAU;AAC/C,WAAK,OAAO;AACZ,WAAK,YAAY,IAAI;AAErB,aAAO,SAAS,KAAK,YAAY,IAAI;AAAA,IACvC;AAEA,WAAO,UAAU,SAAS,KAAK,SAAS,UAAU;AAChD,YAAM,KAAK,OAAO;AAClB,UAAI,CAAC,MAAM;AACT,mBAAW;AAAA,MACb;AACA,UAAI,KAAK,MAAM,OAAO,OAAO,CAAC;AAC9B,WAAK,SAAS;AACd,WAAK,SAAS,SAAS,SAAS,SAAS,QAAQ,KAAK,aAAa,GAAG,OAAO,EAAE;AAE/E,UAAI,SAAS,aAAa,EAAE;AAC5B,aAAO,KAAK;AACZ,aAAO,MAAM,UAAU;AACvB,WAAK,YAAY,MAAM;AAEvB,UAAI;AACF,aAAK,QAAQ;AAAA,MACf,SAAS,GAAG;AAAA,MAEZ;AACA,WAAK,OAAO;AAEZ,UAAI,YAAY,SAAS,KAAK;AAC5B,cAAM,aAAa,IAAI,GAAG;AAC1B,YAAI,CAAC,OAAO,SAAS;AACnB;AAAA,QACF;AACA,eAAO,qBAAqB,OAAO,UAAU,OAAO,SAAS;AAG7D,mBAAW,WAAW;AACpB,gBAAM,eAAe,EAAE;AACvB,iBAAO,WAAW,YAAY,MAAM;AACpC,mBAAS;AAAA,QACX,GAAG,GAAG;AACN,aAAK,QAAQ;AAGb,iBAAS,GAAG;AAAA,MACd;AACA,aAAO,UAAU,WAAW;AAC1B,cAAM,WAAW,EAAE;AACnB,kBAAU;AAAA,MACZ;AACA,aAAO,SAAS,WAAW;AACzB,cAAM,UAAU,EAAE;AAClB,kBAAU;AAAA,MACZ;AACA,aAAO,qBAAqB,SAAS,GAAG;AACtC,cAAM,sBAAsB,IAAI,OAAO,YAAY,CAAC;AACpD,YAAI,OAAO,eAAe,YAAY;AACpC,oBAAU;AAAA,QACZ;AAAA,MACF;AACA,aAAO,WAAW;AAChB,cAAM,WAAW,EAAE;AACnB,kBAAU,IAAI,MAAM,SAAS,CAAC;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;AClGA;AAAA;AAAA;AAUA,QAAI,WAAW;AAAf,QACI,iBAAiB;AADrB,QAEI,gBAAgB;AAFpB,QAGI,cAAc;AAGlB,aAAS,eAAe,UAAU;AAChC,UAAI,CAAC,eAAe,QAAQ,GAAG;AAC7B,cAAM,IAAI,MAAM,iCAAiC;AAAA,MACnD;AACA,qBAAe,KAAK,MAAM,UAAU,UAAU,aAAa,aAAa;AAAA,IAC1E;AAEA,aAAS,gBAAgB,cAAc;AAEvC,mBAAe,UAAU,WAAW;AAClC,aAAO,CAAC,CAAC,OAAO;AAAA,IAClB;AAEA,mBAAe,gBAAgB;AAC/B,mBAAe,aAAa;AAC5B,mBAAe,WAAW;AAE1B,WAAO,UAAU;AAAA;AAAA;;;ACjCjB;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA;AAAA,MAEf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,sBAAuC,sBAAkC;AAAA,MAGzE;AAAA,MACA,sBAAuC,mBAA+B;AAAA,MACtE;AAAA,MACA;AAAA,MACA,sBAAuC,qBAAkC;AAAA,MACzE;AAAA,IACF;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAMA,QAAI,iBAAiB,MAAM;AAC3B,QAAI,kBAAkB,OAAO;AAC7B,QAAI,oBAAoB,SAAS;AACjC,QAAI,kBAAkB,OAAO;AAC7B,QAAI,cAAc,eAAe;AAEjC,QAAI,YAAY,gBAAgB;AAChC,QAAI,aAAa,SAAU,KAAK;AAC5B,aAAO,gBAAgB,SAAS,KAAK,GAAG,MAAM;AAAA,IAClD;AACA,QAAI,UAAU,SAASC,SAAQ,KAAK;AAChC,aAAO,UAAU,KAAK,GAAG,MAAM;AAAA,IACnC;AACA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,aAAO,UAAU,KAAK,GAAG,MAAM;AAAA,IACnC;AAEA,QAAI,sBAAsB,OAAO,kBAAmB,WAAY;AAC5D,UAAI;AACA,eAAO,eAAe,CAAC,GAAG,KAAK,CAAC,CAAC;AACjC,eAAO;AAAA,MACX,SAAS,GAAG;AACR,eAAO;AAAA,MACX;AAAA,IACJ,EAAE;AAIF,QAAI;AACJ,QAAI,qBAAqB;AACrB,uBAAiB,SAAU,QAAQ,MAAM,QAAQ,aAAa;AAC1D,YAAI,CAAC,eAAgB,QAAQ,QAAS;AAAE;AAAA,QAAQ;AAChD,eAAO,eAAe,QAAQ,MAAM;AAAA,UAChC,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,OAAO;AAAA,QACX,CAAC;AAAA,MACL;AAAA,IACJ,OAAO;AACH,uBAAiB,SAAU,QAAQ,MAAM,QAAQ,aAAa;AAC1D,YAAI,CAAC,eAAgB,QAAQ,QAAS;AAAE;AAAA,QAAQ;AAChD,eAAO,IAAI,IAAI;AAAA,MACnB;AAAA,IACJ;AACA,QAAI,mBAAmB,SAAU,QAAQ,KAAK,aAAa;AACvD,eAAS,QAAQ,KAAK;AAClB,YAAI,gBAAgB,eAAe,KAAK,KAAK,IAAI,GAAG;AAClD,yBAAe,QAAQ,MAAM,IAAI,IAAI,GAAG,WAAW;AAAA,QACrD;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,WAAW,SAAU,GAAG;AACxB,UAAI,KAAK,MAAM;AACX,cAAM,IAAI,UAAU,mBAAmB,IAAI,YAAY;AAAA,MAC3D;AACA,aAAO,OAAO,CAAC;AAAA,IACnB;AAWA,aAAS,UAAU,KAAK;AACpB,UAAI,IAAI,CAAC;AACT,UAAI,MAAM,GAAG;AACT,YAAI;AAAA,MACR,WAAW,MAAM,KAAK,MAAO,IAAI,KAAM,MAAM,EAAE,IAAI,IAAI;AACnD,aAAK,IAAI,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AAEA,aAAS,SAAS,GAAG;AACjB,aAAO,MAAM;AAAA,IACjB;AAUA,aAAS,QAAQ;AAAA,IAAC;AAElB,qBAAiB,mBAAmB;AAAA,MAChC,MAAM,SAAS,KAAK,MAAM;AAEtB,YAAI,SAAS;AAEb,YAAI,CAAC,WAAW,MAAM,GAAG;AACrB,gBAAM,IAAI,UAAU,oDAAoD,MAAM;AAAA,QAClF;AAIA,YAAI,OAAO,YAAY,KAAK,WAAW,CAAC;AAUxC,YAAI,SAAS,WAAY;AAErB,cAAI,gBAAgB,OAAO;AAiBvB,gBAAI,SAAS,OAAO;AAAA,cAChB;AAAA,cACA,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC;AAAA,YAC3C;AACA,gBAAI,OAAO,MAAM,MAAM,QAAQ;AAC3B,qBAAO;AAAA,YACX;AACA,mBAAO;AAAA,UAEX,OAAO;AAoBH,mBAAO,OAAO;AAAA,cACV;AAAA,cACA,KAAK,OAAO,YAAY,KAAK,SAAS,CAAC;AAAA,YAC3C;AAAA,UAEJ;AAAA,QAEJ;AAQA,YAAI,cAAc,KAAK,IAAI,GAAG,OAAO,SAAS,KAAK,MAAM;AAIzD,YAAI,YAAY,CAAC;AACjB,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,oBAAU,KAAK,MAAM,CAAC;AAAA,QAC1B;AAQA,YAAI,QAAQ,SAAS,UAAU,sBAAsB,UAAU,KAAK,GAAG,IAAI,4CAA4C,EAAE,MAAM;AAE/H,YAAI,OAAO,WAAW;AAClB,gBAAM,YAAY,OAAO;AACzB,gBAAM,YAAY,IAAI,MAAM;AAE5B,gBAAM,YAAY;AAAA,QACtB;AAuBA,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAUD,qBAAiB,OAAO,EAAE,QAAiB,CAAC;AAG5C,QAAI,cAAc,OAAO,GAAG;AAC5B,QAAI,cAAc,YAAY,CAAC,MAAM,OAAO,EAAE,KAAK;AAEnD,QAAI,uBAAuB,SAAS,cAAc,QAAQ;AAEtD,UAAI,yBAAyB;AAC7B,UAAI,sBAAsB;AAC1B,UAAI,QAAQ;AACR,eAAO,KAAK,OAAO,SAAU,GAAG,IAAI,SAAS;AACzC,cAAI,OAAO,YAAY,UAAU;AAAE,qCAAyB;AAAA,UAAO;AAAA,QACvE,CAAC;AAED,eAAO,KAAK,CAAC,CAAC,GAAG,WAAY;AACzB;AACA,gCAAsB,OAAO,SAAS;AAAA,QAC1C,GAAG,GAAG;AAAA,MACV;AACA,aAAO,CAAC,CAAC,UAAU,0BAA0B;AAAA,IACjD;AAEA,qBAAiB,gBAAgB;AAAA,MAC7B,SAAS,SAAS,QAAQ,KAAiB;AACvC,YAAI,SAAS,SAAS,IAAI,GACtBC,QAAO,eAAe,SAAS,IAAI,IAAI,KAAK,MAAM,EAAE,IAAI,QACxD,QAAQ,UAAU,CAAC,GACnB,IAAI,IACJ,SAASA,MAAK,WAAW;AAG7B,YAAI,CAAC,WAAW,GAAG,GAAG;AAClB,gBAAM,IAAI,UAAU;AAAA,QACxB;AAEA,eAAO,EAAE,IAAI,QAAQ;AACjB,cAAI,KAAKA,OAAM;AAIX,gBAAI,KAAK,OAAOA,MAAK,CAAC,GAAG,GAAG,MAAM;AAAA,UACtC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,GAAG,CAAC,qBAAqB,eAAe,OAAO,CAAC;AAKhD,QAAI,wBAAwB,MAAM,UAAU,WAAW,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,MAAM;AAChF,qBAAiB,gBAAgB;AAAA,MAC7B,SAAS,SAAS,QAAQ,QAA0B;AAChD,YAAIA,QAAO,eAAe,SAAS,IAAI,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS,IAAI,GACrE,SAASA,MAAK,WAAW;AAE7B,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AAEA,YAAI,IAAI;AACR,YAAI,UAAU,SAAS,GAAG;AACtB,cAAI,UAAU,UAAU,CAAC,CAAC;AAAA,QAC9B;AAGA,YAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;AACvC,eAAO,IAAI,QAAQ,KAAK;AACpB,cAAI,KAAKA,SAAQA,MAAK,CAAC,MAAM,QAAQ;AACjC,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ,GAAG,qBAAqB;AAsBxB,QAAI,eAAe,gBAAgB;AACnC,QACI,KAAK,MAAM,SAAS,EAAE,WAAW,KACjC,IAAI,MAAM,UAAU,EAAE,WAAW,KACjC,QAAQ,MAAM,MAAM,EAAE,CAAC,MAAM,OAC7B,OAAO,MAAM,QAAQ,EAAE,EAAE,WAAW,KACpC,GAAG,MAAM,IAAI,EAAE,UACf,IAAI,MAAM,MAAM,EAAE,SAAS,GAC7B;AACE,OAAC,WAAY;AACT,YAAI,oBAAoB,OAAO,KAAK,EAAE,EAAE,CAAC,MAAM;AAE/C,wBAAgB,QAAQ,SAAU,WAAW,OAAO;AAChD,cAAI,SAAS;AACb,cAAI,cAAc,UAAU,UAAU,GAAG;AACrC,mBAAO,CAAC;AAAA,UACZ;AAGA,cAAI,UAAU,KAAK,SAAS,MAAM,mBAAmB;AACjD,mBAAO,aAAa,KAAK,MAAM,WAAW,KAAK;AAAA,UACnD;AAEA,cAAI,SAAS,CAAC,GACV,SAAS,UAAU,aAAa,MAAM,OAC7B,UAAU,YAAa,MAAM,OAC7B,UAAU,WAAa,MAAM;AAAA,WAC7B,UAAU,SAAa,MAAM,KACtC,gBAAgB,GAEhB,YAAY,OAAO,WAAW;AAClC,sBAAY,IAAI,OAAO,UAAU,QAAQ,QAAQ,GAAG;AACpD,oBAAU;AACV,cAAI,CAAC,mBAAmB;AAEpB,yBAAa,IAAI,OAAO,MAAM,UAAU,SAAS,YAAY,KAAK;AAAA,UACtE;AAQA,kBAAQ,UAAU,SACd,OAAO;AAAA;AAAA,YACP,SAAS,KAAK;AAAA;AAClB,iBAAO,QAAQ,UAAU,KAAK,MAAM,GAAG;AAEnC,wBAAY,MAAM,QAAQ,MAAM,CAAC,EAAE;AACnC,gBAAI,YAAY,eAAe;AAC3B,qBAAO,KAAK,OAAO,MAAM,eAAe,MAAM,KAAK,CAAC;AAGpD,kBAAI,CAAC,qBAAqB,MAAM,SAAS,GAAG;AACxC,sBAAM,CAAC,EAAE,QAAQ,YAAY,WAAY;AACrC,2BAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,KAAK;AAC3C,wBAAI,UAAU,CAAC,MAAM,QAAQ;AACzB,4BAAM,CAAC,IAAI;AAAA,oBACf;AAAA,kBACJ;AAAA,gBACJ,CAAC;AAAA,cACL;AACA,kBAAI,MAAM,SAAS,KAAK,MAAM,QAAQ,OAAO,QAAQ;AACjD,+BAAe,KAAK,MAAM,QAAQ,MAAM,MAAM,CAAC,CAAC;AAAA,cACpD;AACA,2BAAa,MAAM,CAAC,EAAE;AACtB,8BAAgB;AAChB,kBAAI,OAAO,UAAU,OAAO;AACxB;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,UAAU,cAAc,MAAM,OAAO;AACrC,wBAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,kBAAkB,OAAO,QAAQ;AACjC,gBAAI,cAAc,CAAC,UAAU,KAAK,EAAE,GAAG;AACnC,qBAAO,KAAK,EAAE;AAAA,YAClB;AAAA,UACJ,OAAO;AACH,mBAAO,KAAK,OAAO,MAAM,aAAa,CAAC;AAAA,UAC3C;AACA,iBAAO,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,KAAK,IAAI;AAAA,QAC5D;AAAA,MACJ,GAAE;AAAA,IAQN,WAAW,IAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ;AACpC,sBAAgB,QAAQ,SAAS,MAAM,WAAW,OAAO;AACrD,YAAI,cAAc,UAAU,UAAU,GAAG;AAAE,iBAAO,CAAC;AAAA,QAAG;AACtD,eAAO,aAAa,KAAK,MAAM,WAAW,KAAK;AAAA,MACnD;AAAA,IACJ;AAOA,QAAI,gBAAgB,gBAAgB;AACpC,QAAI,uBAAuB,GAAG,UAAU,KAAK,OAAO,EAAE,MAAM;AAC5D,qBAAiB,iBAAiB;AAAA,MAC9B,QAAQ,SAAS,OAAO,OAAO,QAAQ;AACnC,eAAO,cAAc;AAAA,UACjB;AAAA,UACA,QAAQ,KAAM,QAAQ,KAAK,SAAS,SAAS,IAAI,IAAI,QAAS;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,GAAG,oBAAoB;AAAA;AAAA;;;ACncvB;AAAA;AAAA;AAKA,QAAI,iBAAiB;AAArB,QACI;AAIJ,QAAI,eAAe,SAAS,WAAW;AACrC,UAAI;AACJ,UAAI,WAAW,CAAC;AAChB,UAAI,IAAI,CAAC;AACT,WAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC1B,UAAE,KAAM,OAAO,aAAa,CAAC,CAAE;AAAA,MACjC;AACA,gBAAU,YAAY;AACtB,QAAE,KAAK,EAAE,EAAE,QAAQ,WAAW,SAAS,GAAG;AACxC,iBAAU,CAAE,IAAI,SAAS,SAAS,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AACxE,eAAO;AAAA,MACT,CAAC;AACD,gBAAU,YAAY;AACtB,aAAO;AAAA,IACT;AAKA,WAAO,UAAU;AAAA,MACf,OAAO,SAAS,QAAQ;AACtB,YAAI,SAAS,KAAK,UAAU,MAAM;AAGlC,uBAAe,YAAY;AAC3B,YAAI,CAAC,eAAe,KAAK,MAAM,GAAG;AAChC,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,aAAa;AAChB,wBAAc,aAAa,cAAc;AAAA,QAC3C;AAEA,eAAO,OAAO,QAAQ,gBAAgB,SAAS,GAAG;AAChD,iBAAO,YAAY,CAAC;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;;;AC/CA;AAAA;AAAA;AAEA,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,+BAA+B;AAAA,IAC1D;AAEA,WAAO,UAAU,SAAS,qBAAqB;AAC7C,aAAO;AAAA,QACL,iBAAiB,SAAS,qBAAqB,MAAM;AACnD,cAAI,aAAa;AAAA,YACf,MAAM,CAAC;AAAA,YACP,QAAQ,CAAC;AAAA,UACX;AACA,cAAI,CAAC,qBAAqB;AACxB,kCAAsB,CAAC;AAAA,UACzB,WAAW,OAAO,wBAAwB,UAAU;AAClD,kCAAsB,CAAC,mBAAmB;AAAA,UAC5C;AAEA,8BAAoB,QAAQ,SAAS,OAAO;AAC1C,gBAAI,CAAC,OAAO;AACV;AAAA,YACF;AAEA,gBAAI,MAAM,kBAAkB,eAAe,KAAK,cAAc,OAAO;AACnE,oBAAM,wBAAwB,WAAW;AACzC;AAAA,YACF;AAEA,gBAAI,oBAAoB,UACpB,oBAAoB,QAAQ,MAAM,aAAa,MAAM,IAAI;AAC3D,oBAAM,oBAAoB,MAAM,aAAa;AAC7C;AAAA,YACF;AAEA,gBAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,oBAAM,WAAW,MAAM,aAAa;AACpC,yBAAW,KAAK,KAAK,KAAK;AAC1B,kBAAI,MAAM,iBAAiB;AACzB,2BAAW,OAAO,KAAK,MAAM,eAAe;AAAA,cAC9C;AAAA,YACF,OAAO;AACL,oBAAM,YAAY,MAAM,aAAa;AAAA,YACvC;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjDA;AAAA;AAAA;AAEA,QAAI,YAAY,CAAC;AACjB,KAAC,OAAO,SAAS,MAAM,EAAE,QAAQ,SAAU,OAAO;AAChD,UAAI;AAEJ,UAAI;AACF,sBAAc,OAAO,WAAW,OAAO,QAAQ,KAAK,KAAK,OAAO,QAAQ,KAAK,EAAE;AAAA,MACjF,SAAQ,GAAG;AAAA,MAEX;AAEA,gBAAU,KAAK,IAAI,cAAc,WAAY;AAC3C,eAAO,OAAO,QAAQ,KAAK,EAAE,MAAM,OAAO,SAAS,SAAS;AAAA,MAC9D,IAAK,UAAU,QAAQ,WAAY;AAAA,MAAC,IAAI,UAAU;AAAA,IACpD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACjBjB,IAAAC,iBAAA;AAAA;AAAA;AAEA,aAAS,MAAM,WAAW;AACxB,WAAK,OAAO;AAAA,IACd;AAEA,UAAM,UAAU,YAAY,SAAS,WAAW,WAAW,YAAY;AACrE,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,aAAa;AAClB,WAAK,YAAY,CAAC,oBAAI,KAAK;AAC3B,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,kBAAkB,WAAW;AAAA,IAAC;AAC9C,UAAM,UAAU,iBAAiB,WAAW;AAAA,IAAC;AAE7C,UAAM,kBAAkB;AACxB,UAAM,YAAY;AAClB,UAAM,iBAAiB;AAEvB,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA;AAEA,WAAO,UAAU,OAAO,YAAY;AAAA,MAClC,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,QAAQ;AAGZ,aAAS,aAAa;AACpB,YAAM,KAAK,IAAI;AACf,WAAK,UAAU,SAAS,OAAO,KAAK;AACpC,WAAK,WAAW;AAChB,WAAK,OAAO;AACZ,WAAK,SAAS;AAAA,IAChB;AAEA,aAAS,YAAY,KAAK;AAE1B,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,QAAQ;AAGZ,aAAS,sBAAsB,MAAM;AACnC,YAAM,KAAK,IAAI;AACf,WAAK,UAAU,WAAW,OAAO,KAAK;AACtC,WAAK,OAAO;AAAA,IACd;AAEA,aAAS,uBAAuB,KAAK;AAErC,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AAGf,aAAS,UAA0C;AACjD,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,WAAK,KAAK,WAAW,WAAW;AAC9B,QAAAA,MAAK,KAAK,UAAU,KAAK,IAAI;AAAA,MAC/B,GAAG,QAAQ,OAAO;AAAA,IACpB;AAEA,aAAS,SAAS,YAAY;AAE9B,YAAQ,UAAU,QAAQ,WAAW;AACnC,mBAAa,KAAK,EAAE;AAAA,IACtB;AAEA,YAAQ,UAAU;AAElB,WAAO,UAAU;AAAA;AAAA;;;ACvBjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AADf,QAEI,cAAc;AAGlB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,yBAAyB;AAAA,IACpD;AAEA,aAAS,SAAS,KAAK,YAAY;AACjC,mBAAa,KAAK,IAAI;AAEtB,UAAIC,QAAO;AACX,UAAI,KAAK,CAAC,oBAAI,KAAK;AACnB,WAAK,KAAK,IAAI,WAAW,OAAO,GAAG;AAEnC,WAAK,GAAG,KAAK,UAAU,SAAS,QAAQ,MAAM;AAC5C,YAAI,MAAM;AACV,YAAI,WAAW,KAAK;AAClB,gBAAO,CAAC,oBAAI,KAAK,IAAK;AACtB,cAAI,MAAM;AACR,gBAAI;AACF,qBAAO,KAAK,MAAM,IAAI;AAAA,YACxB,SAAS,GAAG;AACV,oBAAM,YAAY,IAAI;AAAA,YACxB;AAAA,UACF;AAEA,cAAI,CAAC,YAAY,SAAS,IAAI,GAAG;AAC/B,mBAAO,CAAC;AAAA,UACV;AAAA,QACF;AACA,QAAAA,MAAK,KAAK,UAAU,MAAM,GAAG;AAC7B,QAAAA,MAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,YAAY;AAE/B,aAAS,UAAU,QAAQ,WAAW;AACpC,WAAK,mBAAmB;AACxB,WAAK,GAAG,MAAM;AAAA,IAChB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/CjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,eAAe,kBAAkB;AADrC,QAEI,iBAAiB;AAFrB,QAGI,WAAW;AAGf,aAAS,mBAAmB,UAAU;AACpC,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,WAAK,KAAK,IAAI,SAAS,UAAU,cAAc;AAC/C,WAAK,GAAG,KAAK,UAAU,SAAS,MAAM,KAAK;AACzC,QAAAA,MAAK,KAAK;AACV,QAAAA,MAAK,KAAK,WAAW,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AAAA,MAClD,CAAC;AAAA,IACH;AAEA,aAAS,oBAAoB,YAAY;AAEzC,uBAAmB,gBAAgB;AAEnC,uBAAmB,UAAU,QAAQ,WAAW;AAC9C,UAAI,KAAK,IAAI;AACX,aAAK,GAAG,MAAM;AACd,aAAK,KAAK;AAAA,MACZ;AACA,WAAK,mBAAmB;AAAA,IAC1B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AADf,QAEI,QAAQ;AAFZ,QAGI,kBAAkB;AAHtB,QAII,qBAAqB;AAGzB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,2BAA2B;AAAA,IACtD;AAEA,aAAS,WAAW,SAAS,KAAK;AAChC,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,UAAI,KAAK,WAAW;AAClB,YAAI,MAAMA,MAAK,MAAM,IAAI,gBAAgB,mBAAmB,eAAe,KAAK,OAAO;AAEvF,YAAI,KAAK,WAAW,SAAS,KAAK;AAChC,cAAI,KAAK;AACP,gBAAI;AACJ,gBAAI;AACF,kBAAI,KAAK,MAAM,GAAG;AAAA,YACpB,SAAS,GAAG;AACV,oBAAM,YAAY,GAAG;AACrB,cAAAA,MAAK,KAAK,QAAQ;AAClB,cAAAA,MAAK,MAAM;AACX;AAAA,YACF;AAEA,gBAAI,OAAO,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC;AAC1B,YAAAA,MAAK,KAAK,UAAU,MAAM,GAAG;AAAA,UAC/B;AACA,UAAAA,MAAK,MAAM;AAAA,QACb,CAAC;AAED,YAAI,KAAK,SAAS,WAAW;AAC3B,UAAAA,MAAK,KAAK,QAAQ;AAClB,UAAAA,MAAK,MAAM;AAAA,QACb,CAAC;AAAA,MACH;AAGA,UAAI,CAAC,OAAO,SAAS,MAAM;AACzB,cAAM,YAAY,QAAQ,EAAE;AAAA,MAC9B,OAAO;AACL,WAAG;AAAA,MACL;AAAA,IACF;AAEA,aAAS,YAAY,YAAY;AAEjC,eAAW,UAAU,WAAW;AAC9B,aAAO,gBAAgB,QAAQ;AAAA,IACjC;AAEA,eAAW,UAAU,QAAQ,WAAW;AACtC,UAAI,KAAK,KAAK;AACZ,aAAK,IAAI,MAAM;AAAA,MACjB;AACA,WAAK,mBAAmB;AACxB,WAAK,MAAM;AAAA,IACb;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnEjB;AAAA;AAAA;AAEA,QAAI,eAAe,kBAAkB;AAArC,QACI,WAAW;AADf,QAEI,WAAW;AAFf,QAGI,MAAM;AAHV,QAII,UAAU;AAJd,QAKI,WAAW;AALf,QAMI,UAAU;AANd,QAOI,aAAa;AAPjB,QAQI,WAAW;AAGf,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,6BAA6B;AAAA,IACxD;AAEA,aAAS,aAAa,SAAS,SAAS;AACtC,YAAM,OAAO;AACb,UAAIC,QAAO;AACX,mBAAa,KAAK,IAAI;AAEtB,iBAAW,WAAW;AACpB,QAAAA,MAAK,MAAM,SAAS,OAAO;AAAA,MAC7B,GAAG,CAAC;AAAA,IACN;AAEA,aAAS,cAAc,YAAY;AAInC,iBAAa,eAAe,SAAS,SAAS,KAAK,SAAS;AAE1D,UAAI,QAAQ,YAAY;AACtB,eAAO,IAAI,SAAS,KAAK,QAAQ;AAAA,MACnC;AACA,UAAI,QAAQ,SAAS;AACnB,eAAO,IAAI,SAAS,KAAK,OAAO;AAAA,MAClC;AACA,UAAI,IAAI,WAAW,QAAQ,YAAY;AACrC,eAAO,IAAI,SAAS,KAAK,GAAG;AAAA,MAC9B;AACA,UAAI,WAAW,QAAQ,GAAG;AACxB,eAAO,IAAI,WAAW,SAAS,GAAG;AAAA,MACpC;AACA,aAAO,IAAI,SAAS,KAAK,OAAO;AAAA,IAClC;AAEA,iBAAa,UAAU,QAAQ,SAAS,SAAS,SAAS;AACxD,UAAIA,QAAO,MACP,MAAM,SAAS,QAAQ,SAAS,OAAO;AAE3C,YAAM,SAAS,GAAG;AAElB,WAAK,KAAK,aAAa,aAAa,SAAS,KAAK,OAAO;AAEzD,WAAK,aAAa,WAAW,WAAW;AACtC,cAAM,SAAS;AACf,QAAAA,MAAK,SAAS,KAAK;AACnB,QAAAA,MAAK,KAAK,QAAQ;AAAA,MACpB,GAAG,aAAa,OAAO;AAEvB,WAAK,GAAG,KAAK,UAAU,SAAS,MAAM,KAAK;AACzC,cAAM,UAAU,MAAM,GAAG;AACzB,QAAAA,MAAK,SAAS,IAAI;AAClB,QAAAA,MAAK,KAAK,UAAU,MAAM,GAAG;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,iBAAa,UAAU,WAAW,SAAS,UAAU;AACnD,YAAM,UAAU;AAChB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAClB,UAAI,CAAC,YAAY,KAAK,IAAI;AACxB,aAAK,GAAG,MAAM;AAAA,MAChB;AACA,WAAK,KAAK;AAAA,IACZ;AAEA,iBAAa,UAAU,QAAQ,WAAW;AACxC,YAAM,OAAO;AACb,WAAK,mBAAmB;AACxB,WAAK,SAAS,KAAK;AAAA,IACrB;AAEA,iBAAa,UAAU;AAEvB,WAAO,UAAU;AAAA;AAAA;;;ACxFjB;AAAA;AAAA;AAEA,QAAI,cAAc;AAGlB,aAAS,SAAS,WAAW;AAC3B,WAAK,aAAa;AAClB,gBAAU,GAAG,WAAW,KAAK,kBAAkB,KAAK,IAAI,CAAC;AACzD,gBAAU,GAAG,SAAS,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,IACvD;AAEA,aAAS,UAAU,kBAAkB,SAAS,MAAM,QAAQ;AAC1D,kBAAY,YAAY,KAAK,KAAK,UAAU,CAAC,MAAM,MAAM,CAAC,CAAC;AAAA,IAC7D;AACA,aAAS,UAAU,oBAAoB,SAAS,OAAO;AACrD,kBAAY,YAAY,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,UAAU,QAAQ,SAAS,MAAM;AACxC,WAAK,WAAW,KAAK,IAAI;AAAA,IAC3B;AACA,aAAS,UAAU,SAAS,WAAW;AACrC,WAAK,WAAW,MAAM;AACtB,WAAK,WAAW,mBAAmB;AAAA,IACrC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA;AAEA,QAAI,WAAW;AAAf,QACI,aAAa;AADjB,QAEI,WAAW;AAFf,QAGI,qBAAqB;AAHzB,QAII,cAAc;AAJlB,QAKI,MAAM;AAGV,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,gCAAgC;AAAA,IAC3D;AAEA,WAAO,UAAU,SAAS,QAAQ,qBAAqB;AACrD,UAAI,eAAe,CAAC;AACpB,0BAAoB,QAAQ,SAAS,IAAI;AACvC,YAAI,GAAG,iBAAiB;AACtB,uBAAa,GAAG,gBAAgB,aAAa,IAAI,GAAG;AAAA,QACtD;AAAA,MACF,CAAC;AAID,mBAAa,mBAAmB,aAAa,IAAI;AACjD,UAAI;AAGJ,aAAO,mBAAmB,WAAW;AAEnC,YAAI;AACJ,oBAAY,kBAAkB,IAAI,KAAK,MAAM,CAAC;AAC9C,YAAI,YAAY,SAAS,GAAG;AAC1B,cAAI,EAAE,WAAW,QAAQ;AACvB;AAAA,UACF;AACA,cAAI,OAAO,iBAAiB,aAAa;AACvC,2BAAe,EAAE;AAAA,UACnB;AACA,cAAI,EAAE,WAAW,cAAc;AAC7B;AAAA,UACF;AAEA,cAAI;AACJ,cAAI;AACF,4BAAgB,KAAK,MAAM,EAAE,IAAI;AAAA,UACnC,SAAS,SAAS;AAChB,kBAAM,YAAY,EAAE,IAAI;AACxB;AAAA,UACF;AAEA,cAAI,cAAc,aAAa,YAAY,iBAAiB;AAC1D;AAAA,UACF;AACA,kBAAQ,cAAc,MAAM;AAAA,YAC5B,KAAK;AACH,kBAAI;AACJ,kBAAI;AACF,oBAAI,KAAK,MAAM,cAAc,IAAI;AAAA,cACnC,SAAS,SAAS;AAChB,sBAAM,YAAY,cAAc,IAAI;AACpC;AAAA,cACF;AACA,kBAAI,UAAU,EAAE,CAAC;AACjB,kBAAI,YAAY,EAAE,CAAC;AACnB,kBAAI,WAAW,EAAE,CAAC;AAClB,kBAAI,UAAU,EAAE,CAAC;AACjB,oBAAM,SAAS,WAAW,UAAU,OAAO;AAE3C,kBAAI,YAAY,OAAO,SAAS;AAC9B,sBAAM,IAAI,MAAM,2CACC,UAAU,qBACV,OAAO,UAAU,IAAI;AAAA,cACxC;AAEA,kBAAI,CAAC,SAAS,cAAc,UAAU,IAAI,IAAI,KAC1C,CAAC,SAAS,cAAc,SAAS,IAAI,IAAI,GAAG;AAC9C,sBAAM,IAAI,MAAM,+DACQ,IAAI,OAAO,OAAO,WAAW,OAAO,UAAU,GAAG;AAAA,cAC3E;AACA,uBAAS,IAAI,SAAS,IAAI,aAAa,SAAS,EAAE,UAAU,OAAO,CAAC;AACpE;AAAA,YACF,KAAK;AACH,qBAAO,MAAM,cAAc,IAAI;AAC/B;AAAA,YACF,KAAK;AACH,kBAAI,QAAQ;AACV,uBAAO,OAAO;AAAA,cAChB;AACA,uBAAS;AACT;AAAA,UACF;AAAA,QACF;AAEA,mBAAW,YAAY,WAAW,SAAS;AAG3C,oBAAY,YAAY,GAAG;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA;;;ACpGA;AAAA;AAAA;AAEA;AAEA,QAAI,MAAM;AAAV,QACI,WAAW;AADf,QAEI,SAAS;AAFb,QAGI,SAAS;AAHb,QAII,WAAW;AAJf,QAKI,aAAa;AALjB,QAMI,YAAY;AANhB,QAOI,cAAc;AAPlB,QAQI,UAAU;AARd,QASI,MAAM;AATV,QAUI,QAAQ;AAVZ,QAWI,cAAc;AAXlB,QAYI,MAAM;AAZV,QAaI,aAAa;AAbjB,QAcI,wBAAwB;AAd5B,QAeI,eAAe;AAGnB,QAAI,QAAQ,WAAW;AAAA,IAAC;AACxB,QAAI,MAAuC;AACzC,cAAQ,kBAAiB,oBAAoB;AAAA,IAC/C;AAEA,QAAI;AAGJ,aAAS,OAAO,KAAK,WAAW,SAAS;AACvC,UAAI,EAAE,gBAAgB,SAAS;AAC7B,eAAO,IAAI,OAAO,KAAK,WAAW,OAAO;AAAA,MAC3C;AACA,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM,IAAI,UAAU,sEAAsE;AAAA,MAC5F;AACA,kBAAY,KAAK,IAAI;AAErB,WAAK,aAAa,OAAO;AACzB,WAAK,aAAa;AAClB,WAAK,WAAW;AAGhB,gBAAU,WAAW,CAAC;AACtB,UAAI,QAAQ,qBAAqB;AAC/B,YAAI,KAAK,gEAAgE;AAAA,MAC3E;AACA,WAAK,uBAAuB,QAAQ;AACpC,WAAK,oBAAoB,QAAQ,oBAAoB,CAAC;AACtD,WAAK,WAAW,QAAQ,WAAW;AAEnC,UAAI,YAAY,QAAQ,aAAa;AACrC,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,qBAAqB;AAAA,MAC5B,WAAW,OAAO,cAAc,UAAU;AACxC,aAAK,qBAAqB,WAAW;AACnC,iBAAO,OAAO,OAAO,SAAS;AAAA,QAChC;AAAA,MACF,OAAO;AACL,cAAM,IAAI,UAAU,6EAA6E;AAAA,MACnG;AAEA,WAAK,UAAU,QAAQ,UAAU,OAAO,aAAa,GAAI;AAGzD,UAAI,YAAY,IAAI,IAAI,GAAG;AAC3B,UAAI,CAAC,UAAU,QAAQ,CAAC,UAAU,UAAU;AAC1C,cAAM,IAAI,YAAY,cAAc,MAAM,cAAc;AAAA,MAC1D,WAAW,UAAU,MAAM;AACzB,cAAM,IAAI,YAAY,qCAAqC;AAAA,MAC7D,WAAW,UAAU,aAAa,WAAW,UAAU,aAAa,UAAU;AAC5E,cAAM,IAAI,YAAY,2DAA2D,UAAU,WAAW,mBAAmB;AAAA,MAC3H;AAEA,UAAI,SAAS,UAAU,aAAa;AAEpC,UAAI,IAAI,aAAa,YAAY,CAAC,QAAQ;AAExC,YAAI,CAAC,SAAS,eAAe,UAAU,QAAQ,GAAG;AAChD,gBAAM,IAAI,MAAM,iGAAiG;AAAA,QACnH;AAAA,MACF;AAIA,UAAI,CAAC,WAAW;AACd,oBAAY,CAAC;AAAA,MACf,WAAW,CAAC,MAAM,QAAQ,SAAS,GAAG;AACpC,oBAAY,CAAC,SAAS;AAAA,MACxB;AAGA,UAAI,kBAAkB,UAAU,KAAK;AACrC,sBAAgB,QAAQ,SAAS,OAAO,GAAG;AACzC,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,YAAY,0BAA0B,QAAQ,eAAe;AAAA,QACzE;AACA,YAAI,IAAK,gBAAgB,SAAS,KAAM,UAAU,gBAAgB,IAAI,CAAC,GAAG;AACxE,gBAAM,IAAI,YAAY,0BAA0B,QAAQ,kBAAkB;AAAA,QAC5E;AAAA,MACF,CAAC;AAGD,UAAI,IAAI,SAAS,UAAU,IAAI,IAAI;AACnC,WAAK,UAAU,IAAI,EAAE,YAAY,IAAI;AAGrC,gBAAU,IAAI,YAAY,UAAU,SAAS,QAAQ,QAAQ,EAAE,CAAC;AAGhE,WAAK,MAAM,UAAU;AACrB,YAAM,aAAa,KAAK,GAAG;AAK3B,WAAK,WAAW;AAAA,QACd,YAAY,CAAC,QAAQ,UAAU;AAAA,QAC/B,YAAY,SAAS,cAAc,KAAK,KAAK,IAAI,IAAI;AAAA,QACrD,YAAY,SAAS,cAAc,KAAK,KAAK,IAAI,IAAI;AAAA,MACvD;AAEA,WAAK,MAAM,IAAI,aAAa,KAAK,KAAK,KAAK,QAAQ;AACnD,WAAK,IAAI,KAAK,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,IACtD;AAEA,aAAS,QAAQ,WAAW;AAE5B,aAAS,YAAY,MAAM;AACzB,aAAO,SAAS,OAAS,QAAQ,OAAQ,QAAQ;AAAA,IACnD;AAEA,WAAO,UAAU,QAAQ,SAAS,MAAM,QAAQ;AAE9C,UAAI,QAAQ,CAAC,YAAY,IAAI,GAAG;AAC9B,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACpD;AAEA,UAAI,UAAU,OAAO,SAAS,KAAK;AACjC,cAAM,IAAI,YAAY,uCAAuC;AAAA,MAC/D;AAGA,UAAI,KAAK,eAAe,OAAO,WAAW,KAAK,eAAe,OAAO,QAAQ;AAC3E;AAAA,MACF;AAGA,UAAI,WAAW;AACf,WAAK,OAAO,QAAQ,KAAM,UAAU,kBAAkB,QAAQ;AAAA,IAChE;AAEA,WAAO,UAAU,OAAO,SAAS,MAAM;AAGrC,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,eAAe,OAAO,YAAY;AACzC,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AACA,UAAI,KAAK,eAAe,OAAO,MAAM;AACnC;AAAA,MACF;AACA,WAAK,WAAW,KAAK,OAAO,MAAM,IAAI,CAAC;AAAA,IACzC;AAEA,WAAO,UAAU;AAEjB,WAAO,aAAa;AACpB,WAAO,OAAO;AACd,WAAO,UAAU;AACjB,WAAO,SAAS;AAEhB,WAAO,UAAU,eAAe,SAAS,MAAM,KAAK;AAClD,YAAM,gBAAgB,GAAG;AACzB,WAAK,MAAM;AACX,UAAI,CAAC,MAAM;AACT,aAAK,OAAO,MAAM,0BAA0B;AAC5C;AAAA,MACF;AAIA,WAAK,OAAO,KAAK,SAAS,GAAG;AAE7B,WAAK,YAAY,KAAK,WAAW,KAAK,WAAW,KAAK;AACtD,aAAO,YAAY,OAAO,MAAM,KAAK,QAAQ;AAC7C,YAAM,QAAQ,IAAI;AAElB,UAAI,oBAAoB,WAAW,gBAAgB,KAAK,sBAAsB,IAAI;AAClF,WAAK,cAAc,kBAAkB;AACrC,YAAM,KAAK,YAAY,SAAS,qBAAqB;AAErD,WAAK,SAAS;AAAA,IAChB;AAEA,WAAO,UAAU,WAAW,WAAW;AACrC,eAAS,YAAY,KAAK,YAAY,MAAM,GAAG,WAAW,YAAY,KAAK,YAAY,MAAM,GAAG;AAC9F,cAAM,WAAW,UAAU,aAAa;AACxC,YAAI,UAAU,UAAU;AACtB,cAAI,CAAC,OAAO,SAAS,QAChB,OAAO,OAAO,SAAS,eAAe,eACrC,OAAO,SAAS,eAAe,cAC/B,OAAO,SAAS,eAAe,eAAgB;AACnD,kBAAM,kBAAkB;AACxB,iBAAK,YAAY,QAAQ,SAAS;AAClC,uBAAW,YAAY,QAAQ,KAAK,SAAS,KAAK,IAAI,CAAC;AACvD;AAAA,UACF;AAAA,QACF;AAGA,YAAI,YAAY,KAAK,IAAI,KAAK,UAAW,KAAK,OAAO,UAAU,cAAe,GAAI;AAClF,aAAK,sBAAsB,WAAW,KAAK,kBAAkB,KAAK,IAAI,GAAG,SAAS;AAClF,cAAM,iBAAiB,SAAS;AAEhC,YAAI,eAAe,SAAS,QAAQ,KAAK,WAAW,MAAM,KAAK,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACxG,YAAI,UAAU,KAAK,kBAAkB,UAAU,aAAa;AAC5D,cAAM,iBAAiB,YAAY;AACnC,YAAI,eAAe,IAAI,UAAU,cAAc,KAAK,WAAW,OAAO;AACtE,qBAAa,GAAG,WAAW,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAC5D,qBAAa,KAAK,SAAS,KAAK,gBAAgB,KAAK,IAAI,CAAC;AAC1D,qBAAa,gBAAgB,UAAU;AACvC,aAAK,aAAa;AAElB;AAAA,MACF;AACA,WAAK,OAAO,KAAM,yBAAyB,KAAK;AAAA,IAClD;AAEA,WAAO,UAAU,oBAAoB,WAAW;AAC9C,YAAM,mBAAmB;AACzB,UAAI,KAAK,eAAe,OAAO,YAAY;AACzC,YAAI,KAAK,YAAY;AACnB,eAAK,WAAW,MAAM;AAAA,QACxB;AAEA,aAAK,gBAAgB,MAAM,qBAAqB;AAAA,MAClD;AAAA,IACF;AAEA,WAAO,UAAU,oBAAoB,SAAS,KAAK;AACjD,YAAM,qBAAqB,GAAG;AAC9B,UAAIC,QAAO,MACP,OAAO,IAAI,MAAM,GAAG,CAAC,GACrB,UAAU,IAAI,MAAM,CAAC,GACrB;AAIJ,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,eAAK,MAAM;AACX;AAAA,QACF,KAAK;AACH,eAAK,cAAc,IAAI,MAAM,WAAW,CAAC;AACzC,gBAAM,aAAa,KAAK,SAAS;AACjC;AAAA,MACJ;AAEA,UAAI,SAAS;AACX,YAAI;AACF,oBAAU,KAAK,MAAM,OAAO;AAAA,QAC9B,SAAS,GAAG;AACV,gBAAM,YAAY,OAAO;AAAA,QAC3B;AAAA,MACF;AAEA,UAAI,OAAO,YAAY,aAAa;AAClC,cAAM,iBAAiB,OAAO;AAC9B;AAAA,MACF;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,cAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,oBAAQ,QAAQ,SAAS,GAAG;AAC1B,oBAAM,WAAWA,MAAK,WAAW,CAAC;AAClC,cAAAA,MAAK,cAAc,IAAI,sBAAsB,CAAC,CAAC;AAAA,YACjD,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AACH,gBAAM,WAAW,KAAK,WAAW,OAAO;AACxC,eAAK,cAAc,IAAI,sBAAsB,OAAO,CAAC;AACrD;AAAA,QACF,KAAK;AACH,cAAI,MAAM,QAAQ,OAAO,KAAK,QAAQ,WAAW,GAAG;AAClD,iBAAK,OAAO,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,IAAI;AAAA,UAC1C;AACA;AAAA,MACJ;AAAA,IACF;AAEA,WAAO,UAAU,kBAAkB,SAAS,MAAM,QAAQ;AACxD,YAAM,mBAAmB,KAAK,WAAW,MAAM,MAAM;AACrD,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,mBAAmB;AACnC,aAAK,aAAa;AAClB,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI,CAAC,YAAY,IAAI,KAAK,SAAS,OAAQ,KAAK,eAAe,OAAO,YAAY;AAChF,aAAK,SAAS;AACd;AAAA,MACF;AAEA,WAAK,OAAO,MAAM,MAAM;AAAA,IAC1B;AAEA,WAAO,UAAU,QAAQ,WAAW;AAClC,YAAM,SAAS,KAAK,cAAc,KAAK,WAAW,eAAe,KAAK,UAAU;AAChF,UAAI,KAAK,eAAe,OAAO,YAAY;AACzC,YAAI,KAAK,qBAAqB;AAC5B,uBAAa,KAAK,mBAAmB;AACrC,eAAK,sBAAsB;AAAA,QAC7B;AACA,aAAK,aAAa,OAAO;AACzB,aAAK,YAAY,KAAK,WAAW;AACjC,aAAK,cAAc,IAAI,MAAM,MAAM,CAAC;AACpC,cAAM,aAAa,KAAK,SAAS;AAAA,MACnC,OAAO;AAGL,aAAK,OAAO,MAAM,qBAAqB;AAAA,MACzC;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,SAAS,MAAM,QAAQ,UAAU;AACzD,YAAM,UAAU,KAAK,WAAW,MAAM,QAAQ,UAAU,KAAK,UAAU;AACvE,UAAI,YAAY;AAEhB,UAAI,KAAK,KAAK;AACZ,oBAAY;AACZ,aAAK,IAAI,MAAM;AACf,aAAK,MAAM;AAAA,MACb;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,MAAM;AACtB,aAAK,aAAa;AAClB,aAAK,YAAY;AAAA,MACnB;AAEA,UAAI,KAAK,eAAe,OAAO,QAAQ;AACrC,cAAM,IAAI,MAAM,mDAAmD;AAAA,MACrE;AAEA,WAAK,aAAa,OAAO;AACzB,kBAAW,WAAW;AACpB,aAAK,aAAa,OAAO;AAEzB,YAAI,WAAW;AACb,eAAK,cAAc,IAAI,MAAM,OAAO,CAAC;AAAA,QACvC;AAEA,YAAI,IAAI,IAAI,WAAW,OAAO;AAC9B,UAAE,WAAW,YAAY;AACzB,UAAE,OAAO,QAAQ;AACjB,UAAE,SAAS;AAEX,aAAK,cAAc,CAAC;AACpB,aAAK,YAAY,KAAK,UAAU,KAAK,UAAU;AAC/C,cAAM,cAAc;AAAA,MACtB,GAAE,KAAK,IAAI,GAAG,CAAC;AAAA,IACjB;AAIA,WAAO,UAAU,WAAW,SAAS,KAAK;AAOxC,UAAI,MAAM,KAAK;AACb,eAAO,IAAI;AAAA,MACb;AACA,aAAO,MAAM;AAAA,IACf;AAEA,WAAO,UAAU,SAAS,qBAAqB;AAC7C,mBAAa,UAAU,mBAAmB;AAC1C,iCAA8B,QAAQ,mBAAmB;AACzD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnYA;AAAA;AAEA,QAAI,gBAAgB;AAEpB,WAAO,UAAU,eAAkB,aAAa;AAGhD,QAAI,oBAAoB,QAAQ;AAC9B,iBAAW,OAAO,gBAAgB,CAAC;AAAA,IACrC;AAAA;AAAA;", "names": ["self", "_typeof", "obj", "self", "require_websocket", "self", "self", "self", "self", "self", "self", "require_browser", "self", "require_eventsource", "self", "require_eventsource", "require_iframe", "self", "self", "require_htmlfile", "self", "require_jsonp", "isArray", "isString", "self", "require_event", "self", "self", "self", "self", "self", "self"]}