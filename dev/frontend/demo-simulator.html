<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WAIC Demo 演示模拟器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .step {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
        }
        .step.active { border-left-color: #007bff; background: #e3f2fd; }
        .step.complete { border-left-color: #28a745; background: #d4edda; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .demo-link {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px 0;
        }
        .demo-link:hover {
            background: #218838;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 WAIC Demo 演示模拟器</h1>
        <p>这个页面可以帮助您测试和演示WAIC Demo的完整功能流程。</p>
        
        <div class="grid">
            <div>
                <h2>📋 演示控制</h2>
                
                <div id="connectionStatus" class="status info">
                    状态：准备就绪
                </div>
                
                <div class="controls">
                    <button onclick="openDemo()">🚀 打开演示页面</button>
                    <button onclick="openTestPage()">🔧 打开测试页面</button>
                    <button onclick="simulateFullDemo()">▶️ 模拟完整演示</button>
                    <button onclick="clearLog()">🗑️ 清除日志</button>
                </div>
                
                <h3>演示步骤</h3>
                <div id="steps">
                    <div class="step" id="step0">1. 系统初始化</div>
                    <div class="step" id="step1">2. APP连接模拟</div>
                    <div class="step" id="step2">3. 开始新演示</div>
                    <div class="step" id="step3">4. 拍照上传</div>
                    <div class="step" id="step4">5. 人脸检测</div>
                    <div class="step" id="step5">6. 视频生成</div>
                    <div class="step" id="step6">7. 深伪检测</div>
                    <div class="step" id="step7">8. AI分析</div>
                    <div class="step" id="step8">9. 结果展示</div>
                </div>
                
                <h3>快速操作</h3>
                <div class="controls">
                    <button onclick="simulatePhotoUpload()">📸 模拟拍照上传</button>
                    <button onclick="simulateVideoGeneration()">🎥 模拟视频生成</button>
                    <button onclick="simulateDetection()">🛡️ 模拟检测结果</button>
                    <button onclick="simulateAIAnalysis()">🧠 模拟AI分析</button>
                </div>
            </div>
            
            <div>
                <h2>📊 演示日志</h2>
                <div id="log" class="log"></div>
                
                <h3>🔗 快速链接</h3>
                <a href="http://localhost:3000" target="_blank" class="demo-link">
                    🎯 主演示页面
                </a>
                <br>
                <a href="http://localhost:3000/test-websocket.html" target="_blank" class="demo-link">
                    🔧 WebSocket测试页面
                </a>
                
                <h3>📝 使用说明</h3>
                <ul>
                    <li><strong>打开演示页面</strong>：在新窗口中打开主演示界面</li>
                    <li><strong>模拟完整演示</strong>：自动执行完整的演示流程</li>
                    <li><strong>WebSocket测试</strong>：测试与后端的连接和通信</li>
                    <li><strong>快速操作</strong>：单独测试各个功能模块</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🖥️ 演示预览</h2>
        <p>下面是嵌入的演示页面，您可以直接在这里进行操作：</p>
        <iframe src="http://localhost:3000" class="demo-frame" id="demoFrame"></iframe>
    </div>

    <script>
        let currentStep = 0;
        const steps = [
            '系统初始化',
            'APP连接模拟', 
            '开始新演示',
            '拍照上传',
            '人脸检测',
            '视频生成', 
            '深伪检测',
            'AI分析',
            '结果展示'
        ];
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = `状态：${message}`;
            statusElement.className = `status ${type}`;
        }
        
        function updateStep(stepIndex, status = 'active') {
            // 重置所有步骤
            for (let i = 0; i < steps.length; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.className = 'step';
                if (i < stepIndex) {
                    stepElement.className = 'step complete';
                } else if (i === stepIndex) {
                    stepElement.className = `step ${status}`;
                }
            }
            currentStep = stepIndex;
        }
        
        function openDemo() {
            window.open('http://localhost:3000', '_blank');
            log('🚀 已打开演示页面');
        }
        
        function openTestPage() {
            window.open('http://localhost:3000/test-websocket.html', '_blank');
            log('🔧 已打开WebSocket测试页面');
        }
        
        async function simulateFullDemo() {
            log('🎬 开始模拟完整演示流程');
            updateStatus('演示进行中', 'warning');
            
            // 步骤1: 系统初始化
            updateStep(0);
            log('1️⃣ 系统初始化...');
            await sleep(1000);
            
            // 步骤2: APP连接
            updateStep(1);
            log('2️⃣ 模拟APP连接...');
            await sleep(2000);
            
            // 步骤3: 开始演示
            updateStep(2);
            log('3️⃣ 开始新演示...');
            await sleep(1500);
            
            // 步骤4: 拍照上传
            updateStep(3);
            log('4️⃣ 等待拍照上传...');
            await sleep(3000);
            
            // 步骤5: 人脸检测
            updateStep(4);
            log('5️⃣ 人脸检测中...');
            await sleep(2000);
            
            // 步骤6: 视频生成
            updateStep(5);
            log('6️⃣ 视频生成中...');
            for (let i = 0; i <= 100; i += 10) {
                log(`   📊 视频生成进度: ${i}%`);
                await sleep(500);
            }
            
            // 步骤7: 深伪检测
            updateStep(6);
            log('7️⃣ 深伪检测中...');
            await sleep(3000);
            
            // 步骤8: AI分析
            updateStep(7);
            log('8️⃣ AI分析中...');
            await sleep(2000);
            
            // 步骤9: 完成
            updateStep(8, 'complete');
            log('9️⃣ 演示完成！');
            updateStatus('演示完成', 'success');
            
            log('✅ 完整演示流程模拟完成');
            log('⏰ 2分钟后将自动开始新演示...');
        }
        
        async function simulatePhotoUpload() {
            log('📸 模拟拍照上传...');
            updateStep(3);
            await sleep(2000);
            log('✅ 照片上传成功');
        }
        
        async function simulateVideoGeneration() {
            log('🎥 模拟视频生成...');
            updateStep(5);
            for (let i = 0; i <= 100; i += 20) {
                log(`   📊 视频生成进度: ${i}%`);
                await sleep(300);
            }
            log('✅ 视频生成完成');
        }
        
        async function simulateDetection() {
            log('🛡️ 模拟深伪检测...');
            updateStep(6);
            await sleep(2000);
            log('✅ 检测完成 - 置信度: 95.7%');
        }
        
        async function simulateAIAnalysis() {
            log('🧠 模拟AI分析...');
            updateStep(7);
            await sleep(3000);
            log('✅ AI分析完成');
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            log('🗑️ 日志已清除');
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('🚀 演示模拟器已加载');
            log('💡 提示：点击"模拟完整演示"开始自动演示流程');
            updateStatus('准备就绪', 'success');
            
            // 检查演示页面是否可访问
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        log('✅ 演示页面服务正常');
                    } else {
                        log('⚠️ 演示页面响应异常');
                    }
                })
                .catch(error => {
                    log('❌ 无法连接到演示页面，请确保服务已启动');
                    updateStatus('服务未启动', 'error');
                });
        });
    </script>
</body>
</html>
