<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID - Defake深伪检测防御演示</title>
    
    <!-- External CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", <PERSON><PERSON>, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f172a 100%);
            overflow: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* 背景电路板效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px),
                linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: circuitMove 30s linear infinite;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes circuitMove {
            0% { background-position: 0 0; }
            100% { background-position: 50px 50px; }
        }

        #app {
            position: relative;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
