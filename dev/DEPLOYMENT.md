# WAIC Demo 部署和使用指南

## 项目概述

WAIC Demo 是一个深伪检测防御演示项目，包含Vue.js前端和WebSocket通信功能，用于展会现场的大屏幕演示。

## 项目结构

```
WAIC_Demo/
├── dev/
│   ├── frontend/              # Vue.js前端项目
│   │   ├── src/
│   │   │   ├── components/    # Vue组件
│   │   │   ├── services/      # 服务层（WebSocket等）
│   │   │   └── styles/        # 样式文件
│   │   ├── package.json
│   │   └── README.md
│   ├── main.py               # 后端服务（待开发）
│   └── requirements.txt
├── ui_design/                # UI设计参考
├── html.html                # 后端功能模拟页面
├── ws.html                  # 手机端模拟页面
└── README.md               # 项目总体说明
```

## 快速部署

### 1. 环境要求

- Node.js 16+ 
- npm 或 yarn
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）

### 2. 前端部署

```bash
# 进入前端目录
cd dev/frontend

# 安装依赖
npm install

# 开发模式启动
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

### 3. 访问地址

- **开发模式**: http://localhost:3000
- **网络访问**: http://[本机IP]:3000
- **WebSocket测试页面**: http://localhost:3000/test-websocket.html

## 功能特性

### 🎯 大屏展示界面
- 科技感的三栏布局设计
- 适配大屏幕显示器
- 实时状态指示和进度显示

### 🔄 WebSocket实时通信
- 自动连接和重连机制
- 支持图片和视频数据传输
- 实时状态同步

### 📊 演示流程管理
- 6步完整进度时间线
- 自动化演示控制
- 一键启动和重置功能

### 🎨 动画效果
- 脉冲发光效果
- 扫描线动画
- 打字机效果
- 能量波动效果

## 配置说明

### WebSocket服务器配置

编辑 `src/services/websocket.js`：

```javascript
// 修改WebSocket服务器地址
const serverUrl = 'http://your-server:port/ws'
```

### 演示流程配置

编辑 `src/services/demoController.js`：

```javascript
// 修改各步骤持续时间
this.steps = [
  { name: 'faceDetection', duration: 2000 },    // 人脸检测
  { name: 'videoGeneration', duration: 15000 }, // 视频生成
  { name: 'deepfakeDetection', duration: 8000 }, // 深伪检测
  { name: 'aiExplanation', duration: 5000 }     // AI分析
]

// 修改自动重启时间（秒）
this.countdownSeconds.value = 120 // 2分钟
```

## 使用流程

### 展会现场操作

1. **系统准备**
   - 打开浏览器访问演示页面
   - 确认WebSocket连接状态
   - 等待APP连接模拟完成

2. **开始演示**
   - 点击"开始新演示"按钮
   - 系统自动开启拍照功能（1.5秒后）
   - 等待用户使用手机APP拍照上传

3. **自动处理**
   - 人脸检测 → 视频生成 → 深伪检测 → AI分析
   - 实时显示处理进度和结果
   - 展示检测结果和AI分析内容

4. **自动循环**
   - 演示完成后2分钟自动开始新演示
   - 或手动点击"开始新演示"立即重启

### 测试和调试

1. **WebSocket连接测试**
   ```bash
   # 访问测试页面
   http://localhost:3000/test-websocket.html
   ```

2. **功能测试**
   - 测试WebSocket连接
   - 模拟发送开始演示消息
   - 模拟图片上传功能
   - 查看连接日志和错误信息

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查后端服务是否启动
   - 确认服务器地址和端口
   - 检查网络连接和防火墙

2. **页面显示异常**
   - 清除浏览器缓存
   - 检查控制台错误信息
   - 确认依赖安装完整

3. **动画效果不显示**
   - 检查CSS文件是否正确加载
   - 确认浏览器支持CSS动画
   - 检查是否启用了减少动画设置

### 调试工具

1. **浏览器开发者工具**
   - Console: 查看JavaScript错误
   - Network: 检查资源加载
   - WebSocket: 监控WebSocket连接

2. **Vue DevTools**
   - 安装Vue DevTools浏览器扩展
   - 监控组件状态和数据流

## 性能优化

### 生产环境优化

1. **构建优化**
   ```bash
   npm run build
   ```

2. **静态资源服务**
   - 使用Nginx或Apache服务静态文件
   - 启用Gzip压缩
   - 配置缓存策略

3. **CDN加速**
   - 将静态资源部署到CDN
   - 优化图片和视频加载

### 大屏幕适配

1. **分辨率适配**
   - 支持1920x1080及以上分辨率
   - 响应式布局自动调整

2. **性能监控**
   - 监控内存使用
   - 检查动画性能
   - 优化长时间运行稳定性

## 扩展开发

### 添加新功能

1. **新增组件**
   ```bash
   # 在src/components/目录下创建新组件
   # 在App.vue中引入和使用
   ```

2. **扩展WebSocket消息**
   ```javascript
   // 在websocket.js中添加新的消息处理
   // 在demoController.js中添加对应的状态管理
   ```

### 自定义样式

1. **修改主题色彩**
   ```css
   /* 在src/styles/animations.css中修改 */
   :root {
     --primary-color: #00d4ff;
     --secondary-color: #0066ff;
   }
   ```

2. **添加新动画**
   ```css
   /* 在animations.css中添加新的动画效果 */
   @keyframes newAnimation {
     /* 动画定义 */
   }
   ```

## 技术支持

### 联系方式
- 项目文档: README.md
- 技术问题: 查看控制台日志
- 功能建议: 参考项目需求文档

### 更新日志
- v1.0.0: 初始版本，基础功能实现
- 包含WebSocket通信、演示流程控制、动画效果等核心功能
