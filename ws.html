<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocket STOMP Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
<h2>WebSocket Demo</h2>



<p>
    <input type="file" id="fileInput" accept="image/*">
    <button onclick="waitPhoto()">等待上传</button>
    <button onclick="upload()">上传文件</button>
    <button onclick="clearLog()">clear</button>
</p>

<pre id="log"></pre>

<script>
    let stompClient = null;

    function log(msg) {
        document.getElementById('log').textContent += msg + '\n';
    }

    function connect() {
        const socket = new SockJS('http://************:8701/ws');  // SpringBoot配置的端点
        stompClient = Stomp.over(socket);

      
        stompClient.connect({ 'device-type': 'SDK' }, frame => {
            log("✅ 连接成功: " + frame);

            // 订阅私有消息
            stompClient.subscribe('/user/sdk/topic/msg', message => {
                log("📩 收到消息: " + message.body);
            });

        }, error => {
            log("❌ 连接失败: " + error);
        });
    }
	function waitPhoto() {
		const msg = {
            type: "waitPhoto"
        };
        stompClient.send('/web/topic/sdk', {}, JSON.stringify(msg));
        log("📤 发送消息: " + JSON.stringify(msg));
	
	}
		function clear() {
		const msg = {
            type: "clear"
        };
        stompClient.send('/web/topic/sdk', {}, JSON.stringify(msg));
        log("📤 发送消息: " + JSON.stringify(msg));
	
	}

    function upload() {


	  if (!selectedFileBase64) {
            log("⚠️ 请先选择一个文件！");
            return;
        }
	  
        const msg = {
            type: "upload",
            imageBase64: selectedFileBase64.split(',')[1]
        };
        // 向服务端发送控制消息，路径去掉 /url/a 前缀部分，由 Spring 处理
        stompClient.send('/web/topic/sdk', {}, JSON.stringify(msg));
        log("📤 发送消息: " + JSON.stringify(msg));
    }

	// 监听文件选择并转 base64
    document.getElementById('fileInput').addEventListener('change', function () {
        const file = this.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function (e) {
            selectedFileBase64 = e.target.result;
            log("📸 文件已读取为 Base64，准备上传");
        };
        reader.readAsDataURL(file);  // 会包含前缀 data:image/png;base64,...
    });
	
    connect();
</script>
</body>
</html>

