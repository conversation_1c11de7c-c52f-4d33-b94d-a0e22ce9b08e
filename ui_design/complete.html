<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完成页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
        @keyframes pop-in {
            0% { transform: scale(0.5); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        .pop-in-animation { animation: pop-in 0.5s ease-out; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <div class="flex flex-col items-center justify-center flex-grow px-6 py-12">
                <div class="w-28 h-28 bg-gradient-to-br from-green-200 via-cyan-100 to-blue-100 rounded-full flex items-center justify-center mb-8 shadow-xl animate-bounce-slow">
                    <i class="fas fa-check-circle text-green-500 text-6xl"></i>
                </div>
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mb-3 tracking-wide">处理完成！</h1>
                <p class="text-lg text-gray-600 mb-10">感谢您的配合，您可以再次体验或关闭页面。</p>
                <button class="w-full bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 text-white py-3 rounded-2xl font-bold shadow-xl hover:scale-105 transition-transform duration-200">再次体验</button>
            </div>
        </div>
    </div>
</body>
</html>