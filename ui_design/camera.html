<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>相机拍照页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #000; border-radius: 32px; overflow: hidden; position: relative; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <!-- Top Bar -->
            <div class="flex items-center justify-between px-6 pt-6 pb-2">
                <button class="text-blue-400 hover:text-blue-600 transition-colors"><i class="fas fa-times"></i></button>
                <span class="text-blue-400 hover:text-blue-600 transition-colors"><i class="fas fa-sync-alt"></i></span>
            </div>
            <!-- Camera Preview -->
            <div class="flex flex-col items-center justify-center flex-grow">
                <div class="relative w-64 h-96 bg-black rounded-3xl overflow-hidden flex items-center justify-center shadow-2xl border-4 border-white border-opacity-60">
                    <!-- Face Outline -->
                    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <svg width="180" height="240" viewBox="0 0 180 240" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <ellipse cx="90" cy="120" rx="80" ry="110" stroke="#00B4D8" stroke-width="4" stroke-dasharray="8 8"/>
                        </svg>
                    </div>
                    <span class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-xs bg-black bg-opacity-40 px-4 py-1 rounded-full shadow">请将人脸对准轮廓</span>
                </div>
            </div>
            <!-- Capture Button -->
            <div class="flex flex-col items-center mb-10">
                <button class="w-20 h-20 bg-gradient-to-br from-blue-400 via-cyan-400 to-purple-400 rounded-full flex items-center justify-center shadow-xl hover:scale-110 transition-transform duration-200">
                    <i class="fas fa-camera text-white text-3xl"></i>
                </button>
                <span class="mt-5 text-gray-500 text-base">点击按钮拍照</span>
            </div>
        </div>
    </div>
</body>
</html>