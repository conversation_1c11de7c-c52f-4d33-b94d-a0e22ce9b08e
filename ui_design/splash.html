<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>启动页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #fff; border-radius: 32px; overflow: hidden; position: relative; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        .fade-in { animation: fadeIn 1.5s ease-in-out; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-100 via-cyan-100 to-purple-100 flex flex-col justify-center items-center text-center p-0 fade-in">
            <div class="flex flex-col justify-center items-center h-full w-full">
                <div class="w-28 h-28 bg-gradient-to-br from-blue-400 via-cyan-300 to-purple-300 rounded-full flex items-center justify-center mb-8 shadow-lg transition-transform duration-500 hover:scale-105">
                    <i class="fas fa-shield-alt text-6xl text-white drop-shadow-lg"></i>
                </div>
                <h1 class="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mb-2 tracking-wide">SSID Defake</h1>
                <p class="text-lg text-gray-600 mb-6">AI驱动的深伪检测防御</p>
                <div class="w-32 h-2 bg-gradient-to-r from-blue-300 via-cyan-300 to-purple-300 rounded-full opacity-60 animate-pulse"></div>
            </div>
        </div>
    </div>
</body>
</html>