<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col">
            <!-- Status Bar -->
            <div class="flex justify-between items-center px-6 py-2 text-sm bg-transparent">
                <span class="font-semibold text-blue-500">9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-blue-400 text-xs"></i>
                    <i class="fas fa-wifi text-blue-400 text-xs"></i>
                    <i class="fas fa-battery-full text-blue-400 text-xs"></i>
                </div>
            </div>
            <!-- Illustration -->
            <div class="flex-grow flex flex-col justify-center items-center px-10">
                <img src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60" class="rounded-3xl shadow-2xl mb-8 w-60 h-60 object-cover border-4 border-white">
                <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mb-3 tracking-wide">开启您的AI安全之旅</h1>
                <p class="text-base text-gray-600 mb-8">体验业界领先的深伪检测技术，仅需几步即可完成。</p>
                <button class="w-full py-4 bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 text-white font-bold rounded-xl shadow-lg hover:scale-105 transition-transform duration-300 text-lg mb-4">开始体验</button>
                <p class="text-xs text-gray-400 text-center">所有数据仅用于本次演示</p>
            </div>
        </div>
    </div>
</body>
</html>