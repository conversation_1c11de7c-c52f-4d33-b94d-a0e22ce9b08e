<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>照片预览页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <!-- Header -->
            <div class="flex items-center px-6 py-4 mb-6">
                <button class="text-blue-400 hover:text-blue-600 transition-colors"><i class="fas fa-arrow-left"></i></button>
                <h1 class="text-2xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mx-auto tracking-wide">照片预览</h1>
            </div>
            <!-- Photo Preview -->
            <div class="flex-grow flex flex-col items-center justify-center mb-8">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" class="rounded-3xl shadow-2xl w-64 h-80 object-cover border-4 border-white border-opacity-60 transition-transform duration-200 hover:scale-105" />
            </div>
            <!-- Actions -->
            <div class="flex space-x-5 px-6 pb-8">
                <button class="flex-1 bg-gray-200 text-gray-700 py-3 rounded-2xl font-bold shadow hover:bg-gray-300 transition-colors">重新选择</button>
                <button class="flex-1 bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 text-white py-3 rounded-2xl font-bold shadow-xl hover:scale-105 transition-transform duration-200">确认上传</button>
            </div>
        </div>
    </div>
</body>
</html>