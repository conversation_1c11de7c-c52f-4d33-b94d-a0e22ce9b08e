<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预设照片页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
        .photo-grid img.selected { border: 4px solid #3b82f6; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <!-- Header -->
            <div class="flex items-center px-6 py-4 mb-6">
                <button class="text-blue-400 hover:text-blue-600 transition-colors"><i class="fas fa-arrow-left"></i></button>
                <h1 class="text-2xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mx-auto tracking-wide">选择预设照片</h1>
            </div>
            <!-- Photos Grid -->
            <div class="flex-grow grid grid-cols-3 gap-5 px-6 mb-8">
                <div class="relative group cursor-pointer">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" class="rounded-3xl shadow-xl border-4 border-blue-400 transition-transform duration-200 group-hover:scale-105" />
                    <span class="absolute top-2 right-2 bg-gradient-to-br from-blue-400 to-cyan-400 text-white rounded-full px-2 py-1 text-xs shadow">已选</span>
                </div>
                <div class="relative group cursor-pointer">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" class="rounded-3xl shadow-xl transition-transform duration-200 group-hover:scale-105" />
                </div>
                <div class="relative group cursor-pointer">
                    <img src="https://randomuser.me/api/portraits/men/65.jpg" class="rounded-3xl shadow-xl transition-transform duration-200 group-hover:scale-105" />
                </div>
            </div>
            <!-- Confirm Button -->
            <div class="px-6 pb-8">
                <button class="w-full bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 text-white py-3 rounded-2xl font-bold shadow-xl hover:scale-105 transition-transform duration-200">确认选择</button>
            </div>
        </div>
    </div>
</body>
</html>