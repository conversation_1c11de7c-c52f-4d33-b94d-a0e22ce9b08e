<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WAIC Demo - Defake深伪检测防御演示</title>
    
    <!-- External CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #0066ff;
            --secondary-color: #00d4ff;
            --accent-color: #ff6b6b;
            --dark-bg: #0a0e1a;
            --dark-card: #1a1f2e;
            --light-bg: #ffffff;
            --light-card: #f8fafc;
        }

        * {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
        }

        .dark {
            background: linear-gradient(135deg, var(--dark-bg) 0%, #1a1f2e 100%);
            color: #ffffff;
        }

        .light {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: #1a202c;
        }

        .hero-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 102, 255, 0.2);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .flow-arrow {
            animation: flow 3s ease-in-out infinite;
        }

        @keyframes flow {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-waiting { background-color: #fbbf24; }
        .status-processing { background-color: var(--primary-color); animation: pulse 1s infinite; }
        .status-complete { background-color: #10b981; }
        .status-error { background-color: var(--accent-color); }

        .video-placeholder {
            background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
            background-size: 20px 20px;
            background-image: 
                linear-gradient(45deg, rgba(255,255,255,.1) 25%, transparent 25%), 
                linear-gradient(-45deg, rgba(255,255,255,.1) 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, rgba(255,255,255,.1) 75%), 
                linear-gradient(-45deg, transparent 75%, rgba(255,255,255,.1) 75%);
        }

        .mobile-frame {
            background: linear-gradient(145deg, #2d3748, #4a5568);
            border-radius: 25px;
            padding: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .mobile-screen {
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            aspect-ratio: 9/19.5;
        }
    </style>
</head>
<body class="light" id="body">
    <!-- Theme Toggle -->
    <div class="fixed top-4 right-4 z-50">
        <button id="themeToggle" class="p-3 rounded-full bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300">
            <i class="fas fa-moon dark:hidden text-gray-700"></i>
            <i class="fas fa-sun hidden dark:inline text-yellow-400"></i>
        </button>
    </div>

    <!-- Hero Section -->
    <section class="hero-gradient text-white py-20 px-4">
        <div class="max-w-7xl mx-auto text-center">
            <div class="mb-8">
                <i class="fas fa-shield-alt text-6xl mb-4 opacity-90"></i>
                <h1 class="text-5xl md:text-7xl font-bold mb-6">
                    WAIC Demo
                    <span class="block text-3xl md:text-4xl font-normal mt-2 opacity-90">
                        Defake深伪检测防御演示
                    </span>
                </h1>
                <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto leading-relaxed">
                    展现人工智能在内容安全领域的前沿技术，体验从深伪生成到智能检测的完整流程
                </p>
            </div>
            
            <div class="flex justify-center items-center space-x-8 text-sm opacity-80">
                <div class="flex items-center">
                    <i class="fas fa-mobile-alt mr-2"></i>
                    <span>移动端拍照</span>
                </div>
                <i class="fas fa-arrow-right flow-arrow"></i>
                <div class="flex items-center">
                    <i class="fas fa-brain mr-2"></i>
                    <span>AI处理</span>
                </div>
                <i class="fas fa-arrow-right flow-arrow"></i>
                <div class="flex items-center">
                    <i class="fas fa-tv mr-2"></i>
                    <span>大屏展示</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 py-16">
        <!-- System Overview -->
        <section class="mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 dark:text-white">
                <i class="fas fa-sitemap mr-4 text-blue-500"></i>
                系统架构概览
            </h2>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Android APP -->
                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                    <div class="text-center mb-6">
                        <div class="mobile-frame mx-auto w-48">
                            <div class="mobile-screen bg-gradient-to-b from-blue-500 to-blue-600 flex flex-col items-center justify-center text-white p-4">
                                <i class="fas fa-camera text-3xl mb-3"></i>
                                <div class="text-xs mb-2">WAIC Demo</div>
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-3">
                                    <i class="fas fa-user text-lg"></i>
                                </div>
                                <div class="w-12 h-3 bg-white bg-opacity-30 rounded-full mb-2"></div>
                                <div class="flex space-x-2">
                                    <div class="w-8 h-6 bg-white bg-opacity-20 rounded text-xs flex items-center justify-center">重拍</div>
                                    <div class="w-8 h-6 bg-white bg-opacity-40 rounded text-xs flex items-center justify-center">上传</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center dark:text-white">
                        <i class="fas fa-mobile-alt mr-2 text-green-500"></i>
                        Android APP
                    </h3>
                    <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-500"></i>
                            简洁拍照界面
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-500"></i>
                            照片预览确认
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-500"></i>
                            上传进度显示
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-500"></i>
                            Material Design
                        </li>
                    </ul>
                </div>

                <!-- Processing System -->
                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                    <div class="text-center mb-6">
                        <div class="w-32 h-32 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-cogs text-4xl text-white"></i>
                        </div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center dark:text-white">
                        <i class="fas fa-server mr-2 text-purple-500"></i>
                        后端处理系统
                    </h3>
                    <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-purple-500"></i>
                            LivePortrait人脸驱动
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-purple-500"></i>
                            Defake深伪检测
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-purple-500"></i>
                            AI模型解释
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-purple-500"></i>
                            实时状态推送
                        </li>
                    </ul>
                </div>

                <!-- Display System -->
                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                    <div class="text-center mb-6">
                        <div class="w-32 h-20 mx-auto bg-gradient-to-r from-blue-600 to-cyan-500 rounded-lg flex items-center justify-center mb-4">
                            <i class="fas fa-desktop text-2xl text-white"></i>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">大屏幕展示界面预览</div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center dark:text-white">
                        <i class="fas fa-tv mr-2 text-blue-500"></i>
                        大屏展示系统
                    </h3>
                    <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-blue-500"></i>
                            实时状态显示
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-blue-500"></i>
                            视频播放展示
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-blue-500"></i>
                            检测结果可视化
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check-circle mr-3 text-blue-500"></i>
                            科技感界面设计
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Demo Flow Visualization -->
        <section class="mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 dark:text-white">
                <i class="fas fa-route mr-4 text-indigo-500"></i>
                演示流程可视化
            </h2>

            <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-2xl">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-6 items-center">
                    <!-- Step 1 -->
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-camera text-white text-xl"></i>
                        </div>
                        <h4 class="font-bold text-sm dark:text-white mb-2">用户拍照</h4>
                        <div class="status-indicator status-complete"></div>
                        <span class="text-xs text-gray-500">已完成</span>
                    </div>

                    <div class="hidden md:flex justify-center">
                        <i class="fas fa-arrow-right text-2xl text-gray-400 flow-arrow"></i>
                    </div>

                    <!-- Step 2 -->
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-upload text-white text-xl"></i>
                        </div>
                        <h4 class="font-bold text-sm dark:text-white mb-2">照片上传</h4>
                        <div class="status-indicator status-processing"></div>
                        <span class="text-xs text-gray-500">处理中</span>
                    </div>

                    <div class="hidden md:flex justify-center">
                        <i class="fas fa-arrow-right text-2xl text-gray-400 flow-arrow"></i>
                    </div>

                    <!-- Step 3 -->
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-magic text-white text-xl"></i>
                        </div>
                        <h4 class="font-bold text-sm dark:text-white mb-2">AI处理</h4>
                        <div class="status-indicator status-waiting"></div>
                        <span class="text-xs text-gray-500">等待中</span>
                    </div>

                    <div class="hidden md:flex justify-center">
                        <i class="fas fa-arrow-right text-2xl text-gray-400"></i>
                    </div>

                    <!-- Step 4 -->
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-tv text-white text-xl"></i>
                        </div>
                        <h4 class="font-bold text-sm dark:text-white mb-2">结果展示</h4>
                        <div class="status-indicator status-waiting"></div>
                        <span class="text-xs text-gray-500">等待中</span>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-8">
                    <div class="flex justify-between text-sm text-gray-500 mb-2">
                        <span>整体进度</span>
                        <span>35%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-1000" style="width: 35%"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Large Screen Display Preview -->
        <section class="mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 dark:text-white">
                <i class="fas fa-desktop mr-4 text-cyan-500"></i>
                大屏幕展示界面预览
            </h2>

            <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-3xl p-8 shadow-2xl">
                <!-- Header -->
                <div class="text-center mb-8">
                    <h3 class="text-3xl font-bold text-white mb-2">WAIC 2024 - Defake深伪检测防御演示</h3>
                    <div class="flex justify-center items-center space-x-4 text-cyan-400">
                        <i class="fas fa-shield-alt"></i>
                        <span>实时演示进行中</span>
                        <div class="w-3 h-3 bg-green-400 rounded-full pulse-animation"></div>
                    </div>
                </div>

                <!-- Main Display Area -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- Original Photo -->
                    <div class="bg-gray-800 rounded-2xl p-6">
                        <h4 class="text-white font-bold mb-4 flex items-center">
                            <i class="fas fa-image mr-2 text-blue-400"></i>
                            原始照片
                        </h4>
                        <div class="aspect-square bg-gray-700 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user text-4xl text-gray-500"></i>
                        </div>
                        <div class="mt-4 text-center">
                            <span class="text-green-400 text-sm">
                                <i class="fas fa-check-circle mr-1"></i>
                                上传完成
                            </span>
                        </div>
                    </div>

                    <!-- Processing Status -->
                    <div class="bg-gray-800 rounded-2xl p-6">
                        <h4 class="text-white font-bold mb-4 flex items-center">
                            <i class="fas fa-cog mr-2 text-purple-400 pulse-animation"></i>
                            处理状态
                        </h4>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">人脸检测</span>
                                <i class="fas fa-check text-green-400"></i>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">特征提取</span>
                                <i class="fas fa-check text-green-400"></i>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">视频生成</span>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-600 rounded-full h-2 mr-2">
                                        <div class="bg-blue-400 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-blue-400 text-sm">75%</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">深伪检测</span>
                                <i class="fas fa-clock text-yellow-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Detection Results -->
                    <div class="bg-gray-800 rounded-2xl p-6">
                        <h4 class="text-white font-bold mb-4 flex items-center">
                            <i class="fas fa-search mr-2 text-red-400"></i>
                            检测结果
                        </h4>
                        <div class="text-center">
                            <div class="text-6xl mb-4">
                                <i class="fas fa-hourglass-half text-yellow-400 pulse-animation"></i>
                            </div>
                            <p class="text-gray-400">等待检测完成...</p>
                        </div>
                    </div>
                </div>

                <!-- Video Display Area -->
                <div class="bg-gray-800 rounded-2xl p-6 mb-8">
                    <h4 class="text-white font-bold mb-4 flex items-center">
                        <i class="fas fa-play-circle mr-2 text-green-400"></i>
                        合成视频播放区域
                    </h4>
                    <div class="aspect-video bg-black rounded-xl flex items-center justify-center video-placeholder">
                        <div class="text-center text-gray-400">
                            <i class="fas fa-video text-4xl mb-4"></i>
                            <p>视频生成中，请稍候...</p>
                            <div class="mt-4">
                                <div class="inline-flex items-center space-x-2">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Explanation Area -->
                <div class="bg-gray-800 rounded-2xl p-6">
                    <h4 class="text-white font-bold mb-4 flex items-center">
                        <i class="fas fa-brain mr-2 text-cyan-400"></i>
                        AI解释分析
                    </h4>
                    <div class="bg-gray-700 rounded-xl p-4">
                        <p class="text-gray-400 italic">
                            "检测分析将在视频生成完成后开始，AI将从面部特征、光照一致性、边缘模糊等多个维度分析视频的真实性..."
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Features -->
        <section class="mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 dark:text-white">
                <i class="fas fa-rocket mr-4 text-orange-500"></i>
                核心技术特性
            </h2>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="w-16 h-16 mx-auto bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-eye text-white text-xl"></i>
                    </div>
                    <h3 class="font-bold text-lg mb-2 dark:text-white">LivePortrait</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">先进的人脸驱动技术，实现高质量的面部动画生成</p>
                </div>

                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="w-16 h-16 mx-auto bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-shield-alt text-white text-xl"></i>
                    </div>
                    <h3 class="font-bold text-lg mb-2 dark:text-white">Defake检测</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">深度学习驱动的深伪内容检测，准确识别合成媒体</p>
                </div>

                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="w-16 h-16 mx-auto bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-comments text-white text-xl"></i>
                    </div>
                    <h3 class="font-bold text-lg mb-2 dark:text-white">AI解释</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">大模型驱动的可解释性分析，详细说明检测依据</p>
                </div>

                <div class="card-hover bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="w-16 h-16 mx-auto bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-bolt text-white text-xl"></i>
                    </div>
                    <h3 class="font-bold text-lg mb-2 dark:text-white">实时处理</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">优化的处理流程，实现快速响应和流畅体验</p>
                </div>
            </div>
        </section>

        <!-- Performance Metrics -->
        <section class="mb-20">
            <h2 class="text-4xl font-bold text-center mb-12 dark:text-white">
                <i class="fas fa-chart-line mr-4 text-green-500"></i>
                性能指标
            </h2>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="text-3xl font-bold text-blue-500 mb-2">&lt; 5s</div>
                    <div class="text-gray-600 dark:text-gray-300">照片上传时间</div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="text-3xl font-bold text-purple-500 mb-2">&lt; 30s</div>
                    <div class="text-gray-600 dark:text-gray-300">视频生成时间</div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="text-3xl font-bold text-red-500 mb-2">&lt; 15s</div>
                    <div class="text-gray-600 dark:text-gray-300">深伪检测时间</div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-xl text-center">
                    <div class="text-3xl font-bold text-green-500 mb-2">&lt; 60s</div>
                    <div class="text-gray-600 dark:text-gray-300">整体流程时间</div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-shield-alt text-4xl text-blue-400 mb-4"></i>
                <h3 class="text-2xl font-bold mb-2">WAIC Demo Project</h3>
                <p class="text-gray-400">展现AI技术在内容安全领域的创新应用</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 mb-8">
                <div>
                    <h4 class="font-bold mb-4">核心技术</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>LivePortrait人脸驱动</li>
                        <li>Defake深伪检测</li>
                        <li>AI可解释性分析</li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-bold mb-4">应用场景</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>内容安全检测</li>
                        <li>媒体真实性验证</li>
                        <li>教育演示展示</li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-bold mb-4">技术优势</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>高精度检测算法</li>
                        <li>实时处理能力</li>
                        <li>可解释性分析</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-8">
                <p class="text-gray-400">
                    © 2024 WAIC Demo Project. 展现人工智能技术的无限可能。
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const body = document.getElementById('body');

        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        body.className = currentTheme;

        themeToggle.addEventListener('click', () => {
            const newTheme = body.className === 'light' ? 'dark' : 'light';
            body.className = newTheme;
            localStorage.setItem('theme', newTheme);
        });

        // Simulate real-time updates
        function simulateProgress() {
            const progressBar = document.querySelector('.bg-gradient-to-r');
            const statusIndicators = document.querySelectorAll('.status-indicator');
            const statusTexts = document.querySelectorAll('.text-xs.text-gray-500');

            let progress = 35;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress > 100) progress = 100;

                progressBar.style.width = progress + '%';

                if (progress > 50 && statusIndicators[2]) {
                    statusIndicators[2].className = 'status-indicator status-processing';
                    statusTexts[2].textContent = '处理中';
                }

                if (progress > 80 && statusIndicators[3]) {
                    statusIndicators[3].className = 'status-indicator status-processing';
                    statusTexts[3].textContent = '处理中';
                }

                if (progress >= 100) {
                    clearInterval(interval);
                    statusIndicators.forEach(indicator => {
                        indicator.className = 'status-indicator status-complete';
                    });
                    statusTexts.forEach(text => {
                        text.textContent = '已完成';
                    });
                }
            }, 2000);
        }

        // Start simulation after page load
        window.addEventListener('load', () => {
            setTimeout(simulateProgress, 3000);
        });

        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards for animation
        document.querySelectorAll('.card-hover').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
