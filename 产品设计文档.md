# SSID - Defake深伪检测防御演示 APP产品设计文档

## 1. 产品概述

### 1.1 产品定位
SSID Defake Demo APP是一款专为展会演示设计的移动应用，主要用于人脸采集和深伪检测演示。用户可以通过拍照或选择预设照片的方式参与AI深伪检测的完整流程演示。

### 1.2 目标用户
- **主要用户**：展会参观者、技术体验者
- **使用场景**：展会现场、技术演示活动
- **使用时长**：单次使用2-3分钟

### 1.3 核心价值
- 提供便捷的人脸采集方式
- 降低用户参与门槛（预设照片选项）
- 与大屏幕展示系统无缝配合
- 展现AI技术的先进性和实用性

## 2. 功能需求分析

### 2.1 核心功能
1. **人脸采集功能**
   - 实时相机拍照
   - 预设照片选择
   - 照片预览和确认

2. **上传处理功能**
   - 照片上传到服务器
   - 实时进度显示
   - 状态反馈

3. **演示配合功能**
   - 与大屏幕展示联动
   - 用户引导和提示
   - 流程状态同步

### 2.2 功能优先级
- **P0（必须有）**：拍照功能、预设照片选择、上传功能
- **P1（应该有）**：进度显示、状态反馈、用户引导
- **P2（可以有）**：照片编辑、历史记录

## 3. 用户流程设计

### 3.1 主流程
```
启动APP → 选择采集方式 → 获取照片 → 预览确认 → 上传处理 → 完成引导
```

### 3.2 详细流程

#### 流程1：拍照采集
1. **启动页面** - 显示APP介绍和开始按钮
2. **选择方式** - 选择"拍照"或"预设照片"
3. **相机拍照** - 打开相机，实时预览，拍照
4. **照片预览** - 显示拍摄结果，提供重拍/确认选项
5. **上传处理** - 显示上传进度和处理状态
6. **完成页面** - 引导用户关注大屏幕

#### 流程2：预设照片选择
1. **启动页面** - 显示APP介绍和开始按钮
2. **选择方式** - 选择"拍照"或"预设照片"
3. **照片库** - 展示预设照片网格，用户选择
4. **照片预览** - 显示选择结果，提供重选/确认选项
5. **上传处理** - 显示上传进度和处理状态
6. **完成页面** - 引导用户关注大屏幕

### 3.3 异常流程
- **网络异常**：显示错误提示，提供重试选项
- **相机权限**：引导用户开启权限或选择预设照片
- **上传失败**：显示失败原因，提供重新上传选项

## 4. 页面结构设计

### 4.1 页面列表和详细功能

#### **页面1：启动页面（Splash）**
**用途：** APP启动时的品牌展示和初始化页面
**核心功能：**
- 显示SSID品牌Logo和Defake标识
- 展示加载动画和进度
- 初始化APP必要组件
- 自动跳转到欢迎页面（2-3秒）

#### **页面2：欢迎页面（Welcome）**
**用途：** 介绍APP功能和演示流程，引导用户开始体验
**核心功能：**
- 展示APP主要功能介绍
- 说明深伪检测演示流程
- 隐私说明和数据使用声明
- "开始体验"按钮进入下一步

#### **页面3：选择方式页面（Choose Method）**
**用途：** 让用户选择人脸采集方式（拍照或预设照片）
**核心功能：**
- 两个大按钮：「拍照采集」和「预设照片」
- 每个选项的简要说明和图标
- 为不同用户需求提供灵活选择
- 支持返回上一页

#### **页面4：相机拍照页面（Camera）**
**用途：** 使用设备相机进行实时人脸拍摄
**核心功能：**
- 全屏相机预览界面
- 人脸检测框和引导提示
- 网格线辅助构图
- 大尺寸圆形拍照按钮
- 切换前后摄像头功能
- 返回选择方式页面

#### **页面5：预设照片页面（Preset Photos）**
**用途：** 为不愿使用自己照片的用户提供预设人脸照片选择
**核心功能：**
- 网格布局展示8-12张高质量人像照片
- 照片选中状态明确标识（边框高亮）
- 支持滑动浏览更多照片
- 照片来源多样化（不同性别、年龄、种族）
- 确认选择按钮
- 返回选择方式页面

#### **页面6：照片预览页面（Photo Preview）**
**用途：** 展示用户拍摄或选择的照片，提供确认或重新选择的机会
**核心功能：**
- 大尺寸显示选中的照片
- 照片质量和人脸检测状态提示
- 两个操作按钮：「重新选择」和「确认上传」
- 重新选择返回对应的采集页面
- 确认上传进入处理流程

#### **页面7：上传处理页面（Upload Progress）**
**用途：** 显示照片上传和AI处理的实时进度
**核心功能：**
- 环形进度指示器显示整体进度
- 百分比数字显示具体进度
- 处理阶段文字说明（上传中→AI处理中→完成）
- 预计剩余时间显示
- 网络状态监控和错误处理
- 引导用户关注大屏幕的提示

#### **页面8：完成页面（Complete）**
**用途：** 确认处理完成，引导用户关注大屏幕展示结果
**核心功能：**
- 成功完成的视觉反馈（✓图标）
- 感谢用户参与的文字
- 明确指引用户观看大屏幕
- 大屏幕位置指示（如果需要）
- "重新体验"按钮返回选择方式页面
- 退出APP选项

### 4.2 页面跳转关系
```
启动页面（2-3秒自动）
    ↓
欢迎页面（用户点击"开始体验"）
    ↓
选择方式页面
    ↓
┌─────────────────────────┐
↓                         ↓
相机拍照页面              预设照片页面
↓                         ↓
└─────────────────────────┘
    ↓
照片预览页面
    ↓
上传处理页面
    ↓
完成页面
```

### 4.3 交互细节设计

#### **手势交互**
- **点击**：按钮操作、照片选择
- **滑动**：预设照片页面的照片浏览
- **返回手势**：Android系统返回键支持

#### **状态反馈**
- **按钮状态**：正常、按下、禁用、加载中
- **网络状态**：在线、离线、连接中、错误
- **处理状态**：等待、进行中、成功、失败

#### **错误处理**
- **网络错误**：显示错误信息，提供重试按钮
- **相机权限**：引导开启权限或选择预设照片
- **人脸检测失败**：提示重新拍照或调整角度
- **上传失败**：显示失败原因，提供重新上传选项

## 5. 功能详细设计

### 5.1 预设照片功能
**功能描述**：为不愿意使用自己照片的用户提供预设人脸照片选择

**设计要点**：
- 提供8-12张不同性别、年龄、种族的人脸照片
- 照片质量高，适合AI处理
- 网格布局展示，支持滑动浏览
- 选中状态明确标识
- 照片来源：Unsplash高质量人像照片

**技术实现**：
- 照片存储在云端，通过URL引用
- 支持懒加载优化性能
- 选中状态通过CSS样式变化体现

### 5.2 拍照功能
**功能描述**：使用设备相机进行实时人脸拍摄

**设计要点**：
- 全屏相机预览
- 人脸检测框引导
- 网格线辅助构图
- 大尺寸拍照按钮
- 前置摄像头优先

### 5.3 上传进度功能
**功能描述**：实时显示照片上传和处理进度

**设计要点**：
- 环形进度指示器
- 百分比数字显示
- 处理阶段文字说明
- 预计剩余时间
- 错误状态处理

## 6. 设计规范

### 6.1 视觉风格
- **主色调**：科技蓝 (#667eea) 到紫色 (#764ba2) 渐变
- **辅助色**：灰色系 (#f8f9fa, #6c757d)
- **强调色**：绿色 (#28a745) 成功，红色 (#dc3545) 错误
- **字体**：系统默认字体，清晰易读

### 6.2 布局原则
- **简洁明了**：每个页面专注单一任务
- **大按钮设计**：适合展会现场操作
- **清晰反馈**：每个操作都有明确的状态反馈
- **容错设计**：提供返回和重试机制

### 6.3 交互规范
- **点击反馈**：按钮点击有视觉和触觉反馈
- **加载状态**：长时间操作显示加载动画
- **错误处理**：友好的错误提示和解决方案
- **引导提示**：关键步骤提供操作指引

## 7. 技术要求

### 7.1 性能要求
- **启动时间**：< 2秒
- **页面切换**：< 0.5秒
- **照片上传**：支持断点续传
- **内存占用**：< 100MB

### 7.2 兼容性要求
- **Android版本**：Android 7.0+
- **屏幕尺寸**：支持主流手机尺寸
- **网络环境**：支持WiFi和4G/5G
- **权限要求**：相机、存储、网络

### 7.3 安全要求
- **数据传输**：HTTPS加密
- **照片处理**：服务器端处理后删除
- **隐私保护**：不存储用户个人信息
- **权限最小化**：只申请必要权限

## 8. 成功指标

### 8.1 用户体验指标
- **完成率**：> 90%的用户完成完整流程
- **操作时间**：平均完成时间 < 3分钟
- **错误率**：< 5%的操作出现错误
- **用户满意度**：> 4.5分（5分制）

### 8.2 技术性能指标
- **上传成功率**：> 95%
- **响应时间**：< 3秒
- **崩溃率**：< 0.1%
- **内存泄漏**：0次

## 9. 风险评估

### 9.1 技术风险
- **网络不稳定**：展会现场网络可能不稳定
- **设备兼容性**：不同Android设备的兼容性问题
- **性能问题**：低端设备的性能表现

### 9.2 用户体验风险
- **操作复杂**：用户可能不熟悉操作流程
- **隐私担忧**：用户可能担心人脸数据安全
- **设备权限**：用户可能拒绝授权相机权限

### 9.3 解决方案
- **离线模式**：支持离线拍照，网络恢复后上传
- **兼容性测试**：在主流设备上进行充分测试
- **性能优化**：针对低端设备进行专门优化
- **用户引导**：提供清晰的操作指引和帮助信息
- **隐私说明**：明确说明数据使用方式和安全措施
